# 🎮 Hatch 'n' Wing Bingo Feast - QSR Loyalty Game

A Flutter-based bingo game designed for Quick Service Restaurant (QSR) loyalty programs. Features daily play limits, points-based monetization, and engaging food-themed gameplay.

## 🎯 Game Modes

### 5x5 Quick Game (Caller Mode)
- **Duration**: 2-3 minutes
- **Mechanics**: Chef <PERSON> calls food symbols every 7 seconds
- **Win Condition**: Complete lines, diagonals, or patterns
- **Cost**: 30 points for extra plays

### 3x3 Scratch Card (Instant Win)
- **Duration**: Instant
- **Mechanics**: Scratch to reveal symbols
- **Win Condition**: Match 3 identical food symbols
- **Win Rate**: 25% (1 in 4 cards)
- **Cost**: 50 points for extra plays

### 8x8 Challenge (Caller Mode)
- **Duration**: 5-7 minutes
- **Mechanics**: Extended caller mode with more symbols
- **Win Condition**: Complete patterns on larger grid
- **Cost**: 75 points for extra plays

## 💰 Monetization Features

### Daily Play Limits
- **1 free play per day** for each game mode
- **Extra plays** purchasable with loyalty points
- **Automatic reset** at midnight
- **Engagement driver** for daily app usage

### Points Economy
- **Win Rewards**: 25-100 points based on game mode
- **Consolation Points**: 5-10 points for non-winning plays
- **Extra Play Costs**: 30-75 points depending on game mode
- **Business Integration**: 1000 points = $1 off orders

## 🎨 Features

### Chef Bob Virtual Caller
- **Animated character** with mood states
- **Dynamic voice lines** for each food symbol
- **Celebration animations** for wins
- **Encouragement system** for near-wins

### Food-Themed Design
- **30+ food symbols** (burgers, fries, desserts, drinks)
- **QSR branding** with orange color scheme
- **Restaurant iconography** throughout UI
- **Family-friendly design** (COPPA compliant)

### Technical Features
- **Flutter Web** application
- **Responsive design** for mobile and desktop
- **Local storage** for demo (Firebase-ready)
- **Modular architecture** for easy integration
- **Audio system** (currently disabled for web compatibility)

## 🚀 Getting Started

### Prerequisites
- Flutter SDK (3.0+)
- Web browser for testing
- Git for version control

### Installation
1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd "BINGO Caller"
   ```

2. **Navigate to project directory**
   ```bash
   cd bingo_feast_qsr
   ```

3. **Install dependencies**
   ```bash
   flutter pub get
   ```

4. **Run the application**
   ```bash
   flutter run -d web-server --web-port 8080
   ```

5. **Open in browser**
   ```
   http://localhost:8080
   ```

### Quick Demo
Use the included `serve_game.bat` file for easy local testing:
```bash
./serve_game.bat
```

## 🏗️ Architecture

### Models
- **BingoCard**: Game card generation and win detection
- **FoodSymbol**: Food-themed symbols with categories
- **GameState**: Game session management
- **DailyPlays**: Play limit tracking and monetization
- **ChefBob**: Virtual caller character system

### Services
- **DailyPlaysService**: Play limits and point transactions
- **VirtualCallerService**: Chef Bob animations and voice
- **AudioService**: Sound effects and voice playback
- **RewardsService**: Point calculations and rewards

### Screens
- **Main Menu**: Game mode selection with play status
- **Game Screen**: Unified interface for all game modes
- **Purchase Dialogs**: Extra play monetization flow

## 🎮 Business Integration

### QSR Loyalty Program Integration
- **Modular design** for easy integration into existing apps
- **Points-based economy** compatible with loyalty systems
- **Daily engagement** through play limits
- **Reward redemption** tied to food purchases

### Analytics & Metrics
- **Play frequency** tracking
- **Win rate** monitoring (25% for scratch cards)
- **Point economy** balance
- **User engagement** metrics

### Compliance
- **COPPA-compliant** design for family restaurants
- **Age-appropriate** content and mechanics
- **Privacy-focused** data handling
- **Accessibility** considerations

## 📱 Platform Support

- ✅ **Web** (Primary platform)
- ✅ **Android** (Flutter native)
- ✅ **iOS** (Flutter native)
- ✅ **Desktop** (Windows, macOS, Linux)

## 🔧 Configuration

### Win Rates
- **3x3 Scratch Cards**: 25% win rate
- **5x5 Bingo**: Dynamic based on calling sequence
- **8x8 Bingo**: Balanced for longer gameplay

### Point Economy
- **Starting Points**: 500 (demo)
- **Win Rewards**: 25-100 points
- **Extra Play Costs**: 30-75 points
- **Conversion Rate**: 1000 points = $1 off

## 🤝 Contributing

This is a proprietary QSR loyalty game system. For integration inquiries or customization requests, please contact the development team.

## 📄 License

Proprietary - All rights reserved. Designed for QSR loyalty program integration.

---

**Built with Flutter 💙 | Designed for QSR Success 🍔**
