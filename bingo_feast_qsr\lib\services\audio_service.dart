// 🔊 Audio Service
// This handles all sound effects and <PERSON>'s voice

import 'package:audioplayers/audioplayers.dart';
import '../models/food_symbol.dart';

class AudioService {
  static final AudioService _instance = AudioService._internal();
  factory AudioService() => _instance;
  AudioService._internal();

  final AudioPlayer _voicePlayer = AudioPlayer();
  final AudioPlayer _effectsPlayer = AudioPlayer();
  final AudioPlayer _backgroundPlayer = AudioPlayer();

  bool _soundEnabled = false; // Temporarily disabled for testing
  bool _voiceEnabled = false; // Temporarily disabled for testing
  double _volume = 0.7;

  // Initialize audio service
  Future<void> initialize() async {
    await _voicePlayer.setVolume(_volume);
    await _effectsPlayer.setVolume(_volume);
    await _backgroundPlayer.setVolume(_volume * 0.3); // Background music quieter
  }

  // Getters for settings
  bool get soundEnabled => _soundEnabled;
  bool get voiceEnabled => _voiceEnabled;
  double get volume => _volume;

  // Settings
  void setSoundEnabled(bool enabled) {
    _soundEnabled = enabled;
    if (!enabled) {
      stopAll();
    }
  }

  void setVoiceEnabled(bool enabled) {
    _voiceEnabled = enabled;
    if (!enabled) {
      _voicePlayer.stop();
    }
  }

  void setVolume(double volume) {
    _volume = volume.clamp(0.0, 1.0);
    _voicePlayer.setVolume(_volume);
    _effectsPlayer.setVolume(_volume);
    _backgroundPlayer.setVolume(_volume * 0.3);
  }

  // Play Chef Bob's voice for symbol calling
  Future<void> playSymbolCall(FoodSymbol symbol) async {
    if (!_soundEnabled || !_voiceEnabled) return;

    try {
      // Stop any current voice
      await _voicePlayer.stop();

      // Play the symbol's sound file
      await _voicePlayer.play(AssetSource(symbol.soundPath));
    } catch (e) {
      print('Error playing symbol sound: $e');
      // Fallback to text-to-speech or generic sound
      await playGenericCall();
    }
  }

  // Play generic calling sound (fallback)
  Future<void> playGenericCall() async {
    if (!_soundEnabled || !_voiceEnabled) return;

    try {
      await _voicePlayer.play(AssetSource('assets/sounds/generic_call.mp3'));
    } catch (e) {
      print('Error playing generic call sound: $e');
    }
  }

  // Play Chef Bob's greeting
  Future<void> playGreeting() async {
    if (!_soundEnabled || !_voiceEnabled) return;

    try {
      await _voicePlayer.play(AssetSource('assets/sounds/chef_greeting.mp3'));
    } catch (e) {
      print('Error playing greeting: $e');
    }
  }

  // Play celebration sound
  Future<void> playCelebration() async {
    if (!_soundEnabled) return;

    try {
      await _effectsPlayer.play(AssetSource('assets/sounds/celebration.mp3'));
    } catch (e) {
      print('Error playing celebration: $e');
    }
  }

  // Play win sound
  Future<void> playWin() async {
    if (!_soundEnabled) return;

    try {
      await _effectsPlayer.play(AssetSource('assets/sounds/win.mp3'));
    } catch (e) {
      print('Error playing win sound: $e');
    }
  }

  // Play card mark sound (when player marks a square)
  Future<void> playCardMark() async {
    if (!_soundEnabled) return;

    try {
      await _effectsPlayer.play(AssetSource('assets/sounds/card_mark.mp3'));
    } catch (e) {
      print('Error playing card mark sound: $e');
    }
  }

  // Play scratch sound (for scratch mode)
  Future<void> playScratch() async {
    if (!_soundEnabled) return;

    try {
      await _effectsPlayer.play(AssetSource('assets/sounds/scratch.mp3'));
    } catch (e) {
      print('Error playing scratch sound: $e');
    }
  }

  // Play button click sound
  Future<void> playButtonClick() async {
    if (!_soundEnabled) return;

    try {
      await _effectsPlayer.play(AssetSource('assets/sounds/button_click.mp3'));
    } catch (e) {
      print('Error playing button click: $e');
    }
  }

  // Play background music
  Future<void> playBackgroundMusic() async {
    if (!_soundEnabled) return;

    try {
      await _backgroundPlayer.setReleaseMode(ReleaseMode.loop);
      await _backgroundPlayer.play(AssetSource('assets/sounds/background_music.mp3'));
    } catch (e) {
      print('Error playing background music: $e');
    }
  }

  // Stop background music
  Future<void> stopBackgroundMusic() async {
    await _backgroundPlayer.stop();
  }

  // Stop all audio
  Future<void> stopAll() async {
    await _voicePlayer.stop();
    await _effectsPlayer.stop();
    await _backgroundPlayer.stop();
  }

  // Dispose of audio players
  Future<void> dispose() async {
    await _voicePlayer.dispose();
    await _effectsPlayer.dispose();
    await _backgroundPlayer.dispose();
  }
}