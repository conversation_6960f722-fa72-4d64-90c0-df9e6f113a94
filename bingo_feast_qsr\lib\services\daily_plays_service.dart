// 📅 Daily Plays Service
// This manages daily play limits and extra play purchases

import '../models/daily_plays.dart';
import '../models/bingo_card.dart';

// Simple user profile for points tracking (local storage)
class UserProfile {
  final String userId;
  final int totalPoints;

  UserProfile({required this.userId, required this.totalPoints});
}

// Simple user service for points management (local storage)
class UserService {
  static int _userPoints = 500; // Start with 500 points for demo

  Future<UserProfile> getUserProfile(String userId) async {
    // Simulate async call
    await Future.delayed(const Duration(milliseconds: 100));
    return UserProfile(userId: userId, totalPoints: _userPoints);
  }

  Future<void> updateUserPoints(String userId, int pointsChange) async {
    // Simulate async call
    await Future.delayed(const Duration(milliseconds: 100));
    _userPoints += pointsChange;
    if (_userPoints < 0) _userPoints = 0; // Don't go negative
  }
}

class DailyPlaysService {
  static final DailyPlaysService _instance = DailyPlaysService._internal();
  factory DailyPlaysService() => _instance;
  DailyPlaysService._internal();

  final UserService _userService = UserService();

  // Local storage for demo (in real app, use SharedPreferences or Firebase)
  static final Map<String, DailyPlays> _localStorage = {};

  // Get today's play data for a user
  Future<DailyPlays> getTodaysPlays(String userId) async {
    final today = DateTime.now();
    final dateString = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
    final key = '${userId}_$dateString';

    // Simulate async call
    await Future.delayed(const Duration(milliseconds: 50));

    if (_localStorage.containsKey(key)) {
      final stored = _localStorage[key]!;
      // Check if it's actually today
      if (stored.isToday) {
        return stored;
      }
    }

    // Create new record for today
    final newPlays = DailyPlays.forToday(userId);
    _localStorage[key] = newPlays;
    return newPlays;
  }

  // Save daily plays data
  Future<void> _saveDailyPlays(DailyPlays dailyPlays) async {
    final dateString = dailyPlays.date.toIso8601String().split('T')[0];
    final key = '${dailyPlays.userId}_$dateString';

    // Simulate async call
    await Future.delayed(const Duration(milliseconds: 50));
    _localStorage[key] = dailyPlays;
  }

  // Check if user can play a game mode
  Future<PlayAvailability> checkPlayAvailability(String userId, BingoCardType cardType) async {
    final dailyPlays = await getTodaysPlays(userId);
    final userProfile = await _userService.getUserProfile(userId);

    final canPlayFree = dailyPlays.canPlayFree(cardType);
    final extraPlayCost = DailyPlays.getExtraPlayCost(cardType);
    final hasEnoughPoints = userProfile.totalPoints >= extraPlayCost;

    return PlayAvailability(
      canPlayFree: canPlayFree,
      canBuyExtra: hasEnoughPoints,
      extraPlayCost: extraPlayCost,
      currentPoints: userProfile.totalPoints,
      playsUsedToday: dailyPlays.getTotalPlays(cardType),
    );
  }

  // Record a free play
  Future<bool> recordFreePlay(String userId, BingoCardType cardType) async {
    try {
      final dailyPlays = await getTodaysPlays(userId);

      if (!dailyPlays.canPlayFree(cardType)) {
        return false; // Already used free play
      }

      dailyPlays.recordFreePlay(cardType);
      await _saveDailyPlays(dailyPlays);
      return true;
    } catch (e) {
      print('Error recording free play: $e');
      return false;
    }
  }

  // Purchase and record an extra play
  Future<bool> purchaseExtraPlay(String userId, BingoCardType cardType) async {
    try {
      final dailyPlays = await getTodaysPlays(userId);
      final userProfile = await _userService.getUserProfile(userId);
      final cost = DailyPlays.getExtraPlayCost(cardType);

      if (userProfile.totalPoints < cost) {
        return false; // Not enough points
      }

      // Deduct points from user
      await _userService.updateUserPoints(userId, -cost);

      // Record extra play
      dailyPlays.recordExtraPlay(cardType, cost);
      await _saveDailyPlays(dailyPlays);

      return true;
    } catch (e) {
      print('Error purchasing extra play: $e');
      return false;
    }
  }

  // Get play statistics for a user
  Future<PlayStatistics> getPlayStatistics(String userId) async {
    try {
      final today = DateTime.now();
      final dailyPlays = await getTodaysPlays(userId);

      return PlayStatistics(
        totalPlaysToday: dailyPlays.getTotalPlays(BingoCardType.scratch3x3) +
                        dailyPlays.getTotalPlays(BingoCardType.small5x5) +
                        dailyPlays.getTotalPlays(BingoCardType.large8x8),
        pointsSpentToday: dailyPlays.pointsSpentToday,
        scratch3x3Plays: dailyPlays.getTotalPlays(BingoCardType.scratch3x3),
        bingo5x5Plays: dailyPlays.getTotalPlays(BingoCardType.small5x5),
        bingo8x8Plays: dailyPlays.getTotalPlays(BingoCardType.large8x8),
      );
    } catch (e) {
      print('Error getting play statistics: $e');
      return PlayStatistics.empty();
    }
  }

  // Clean up old daily play records (optional - run periodically)
  Future<void> cleanupOldRecords() async {
    final cutoffDate = DateTime.now().subtract(const Duration(days: 30));
    final cutoffString = cutoffDate.toIso8601String().split('T')[0];

    // Remove old records from local storage
    _localStorage.removeWhere((key, value) =>
        value.date.toIso8601String().split('T')[0].compareTo(cutoffString) < 0);
  }
}

// Helper classes for return values
class PlayAvailability {
  final bool canPlayFree;
  final bool canBuyExtra;
  final int extraPlayCost;
  final int currentPoints;
  final int playsUsedToday;

  PlayAvailability({
    required this.canPlayFree,
    required this.canBuyExtra,
    required this.extraPlayCost,
    required this.currentPoints,
    required this.playsUsedToday,
  });

  bool get canPlay => canPlayFree || canBuyExtra;

  String get statusMessage {
    if (canPlayFree) {
      return 'Free play available!';
    } else if (canBuyExtra) {
      return 'Extra play: $extraPlayCost points';
    } else {
      return 'Need $extraPlayCost points for extra play';
    }
  }
}

class PlayStatistics {
  final int totalPlaysToday;
  final int pointsSpentToday;
  final int scratch3x3Plays;
  final int bingo5x5Plays;
  final int bingo8x8Plays;

  PlayStatistics({
    required this.totalPlaysToday,
    required this.pointsSpentToday,
    required this.scratch3x3Plays,
    required this.bingo5x5Plays,
    required this.bingo8x8Plays,
  });

  factory PlayStatistics.empty() {
    return PlayStatistics(
      totalPlaysToday: 0,
      pointsSpentToday: 0,
      scratch3x3Plays: 0,
      bingo5x5Plays: 0,
      bingo8x8Plays: 0,
    );
  }
}