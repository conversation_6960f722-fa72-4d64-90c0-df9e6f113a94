// 🧪 Win Rate Tester
// This validates that our reward system produces the correct win rates

import '../models/bingo_card.dart';
import '../models/game_state.dart';
import '../services/rewards_service.dart';

class WinRateTester {
  static final RewardsService _rewardsService = RewardsService();

  // Test win rates for both card types
  static void runWinRateTests() {
    print('🧪 Testing Win Rates for Hatch \'n\' Wing Bingo Feast');
    print('=' * 60);

    // Test 5x5 cards
    print('\n📊 Testing 5x5 Cards (Target: 65% win rate)');
    final results5x5 = _rewardsService.testWinRates(BingoCardType.small5x5, 10000);
    _printResults('5x5', results5x5, {
      'totalWinRate': 0.65,
      'bigWinRate': 0.05,
      'mediumWinRate': 0.15,
      'smallWinRate': 0.50,
      'noWinRate': 0.35,
    });

    // Test 8x8 cards
    print('\n📊 Testing 8x8 Cards (Target: 40% win rate)');
    final results8x8 = _rewardsService.testWinRates(BingoCardType.large8x8, 10000);
    _printResults('8x8', results8x8, {
      'totalWinRate': 0.40,
      'bigWinRate': 0.02,
      'mediumWinRate': 0.08,
      'smallWinRate': 0.30,
      'noWinRate': 0.60,
    });

    print('\n✅ Win Rate Testing Complete!');
    print('Note: Results should be within ±2% of target rates');
  }

  // Print formatted results
  static void _printResults(String cardType, Map<String, double> actual, Map<String, double> target) {
    print('\n$cardType Card Results (10,000 simulations):');
    print('-' * 40);

    for (final entry in target.entries) {
      final targetRate = entry.value;
      final actualRate = actual[entry.key] ?? 0.0;
      final difference = (actualRate - targetRate).abs();
      final withinRange = difference <= 0.02; // Within 2%
      final status = withinRange ? '✅' : '❌';

      print('${entry.key.padRight(15)}: '
            '${(actualRate * 100).toStringAsFixed(1)}% '
            '(target: ${(targetRate * 100).toStringAsFixed(1)}%) '
            '$status');
    }
  }

  // Test individual reward generation
  static void testRewardGeneration() {
    print('\n🎁 Testing Reward Generation');
    print('=' * 40);

    final testCard = BingoCard.generate(
      userId: 'test',
      type: BingoCardType.small5x5,
      tier: BingoCardTier.bronze,
    );

    print('\nSample rewards for 5x5 card:');
    for (int i = 0; i < 10; i++) {
      final reward = _rewardsService.calculateReward(testCard, GameMode.caller);
      print('${i + 1}. ${reward.description} (${reward.type.name})');
    }
  }

  // Validate that rewards are economically viable for QSR
  static void validateEconomics() {
    print('\n💰 Economic Validation');
    print('=' * 30);

    // Simulate 1000 games and calculate average cost per game
    double totalCost = 0;
    int gamesSimulated = 1000;

    for (int i = 0; i < gamesSimulated; i++) {
      final testCard = BingoCard.generate(
        userId: 'test',
        type: BingoCardType.small5x5,
        tier: BingoCardTier.bronze,
      );

      final reward = _rewardsService.calculateReward(testCard, GameMode.caller);

      // Estimate cost of rewards
      switch (reward.type) {
        case RewardType.freeItem:
          if (reward.description.contains('Combo')) {
            totalCost += 8.0; // Combo meal cost
          } else if (reward.description.contains('Burger') ||
                     reward.description.contains('Chicken')) {
            totalCost += 5.0; // Main item cost
          } else {
            totalCost += 2.0; // Side item cost
          }
          break;
        case RewardType.giftCard:
          totalCost += reward.value.toDouble();
          break;
        case RewardType.discount:
          // Assume average order of $15
          totalCost += 15.0 * (reward.value / 100);
          break;
        case RewardType.points:
          // Points cost (1000 points = $1)
          totalCost += reward.value / 1000;
          break;
        case RewardType.bonusCard:
          // Cost of providing another card (minimal)
          totalCost += 0.10;
          break;
        default:
          break;
      }
    }

    final averageCostPerGame = totalCost / gamesSimulated;
    print('Average reward cost per game: \$${averageCostPerGame.toStringAsFixed(2)}');
    print('For cards earned at \$10 purchase: ${((averageCostPerGame / 10) * 100).toStringAsFixed(1)}% of purchase value');

    if (averageCostPerGame < 1.50) {
      print('✅ Economics look good! Reward costs are sustainable.');
    } else {
      print('⚠️  Reward costs may be too high. Consider adjusting rates.');
    }
  }

  // Run all tests
  static void runAllTests() {
    runWinRateTests();
    testRewardGeneration();
    validateEconomics();
  }
}