// 🎤 Virtual Caller Service
// This coordinates Chef <PERSON>'s actions, voice, and animations during the game

import 'dart:async';
import '../models/chef_bob.dart';
import '../models/food_symbol.dart';
import '../models/game_state.dart';
import 'audio_service.dart';

// Callback types for UI updates
typedef OnChefBobStateChanged = void Function(ChefBob chefBob);
typedef OnSymbolCalled = void Function(FoodSymbol symbol);
typedef OnGameEvent = void Function(String event, Map<String, dynamic> data);

class VirtualCallerService {
  static final VirtualCallerService _instance = VirtualCallerService._internal();
  factory VirtualCallerService() => _instance;
  VirtualCallerService._internal();

  final ChefBob _chefBob = ChefBob();
  final AudioService _audioService = AudioService();

  Timer? _animationTimer;
  Timer? _speechTimer;

  // Callbacks for UI updates
  OnChefBobStateChanged? onChefBobStateChanged;
  OnSymbolCalled? onSymbolCalled;
  OnGameEvent? onGameEvent;

  // Getters
  ChefBob get chefBob => _chefBob;
  bool get isActive => _animationTimer != null || _speechTimer != null;

  // Initialize the service
  Future<void> initialize() async {
    await _audioService.initialize();
    _chefBob.reset();
    _notifyStateChanged();
  }

  // Start the virtual caller for a game
  Future<void> startCalling(GameState gameState) async {
    if (gameState.mode != GameMode.caller) return;

    // Greet the player
    await greetPlayer();

    // Wait a moment, then start the calling sequence
    await Future.delayed(const Duration(seconds: 2));

    _notifyGameEvent('calling_started', {
      'gameId': gameState.gameId,
      'maxCalls': gameState.maxCalls,
    });
  }

  // Make Chef Bob greet the player
  Future<void> greetPlayer() async {
    _chefBob.greet();
    _notifyStateChanged();

    // Play greeting audio
    await _audioService.playGreeting();

    // Stop speaking after a few seconds
    _speechTimer = Timer(const Duration(seconds: 3), () {
      _chefBob.stopSpeaking();
      _notifyStateChanged();
    });
  }

  // Make Chef Bob call a symbol
  Future<void> callSymbol(FoodSymbol symbol) async {
    // Make Chef Bob announce the symbol
    _chefBob.callSymbol(symbol);
    _notifyStateChanged();

    // Play the symbol's audio
    await _audioService.playSymbolCall(symbol);

    // Notify UI about the called symbol
    onSymbolCalled?.call(symbol);

    _notifyGameEvent('symbol_called', {
      'symbol': symbol.toJson(),
      'message': _chefBob.currentMessage,
    });

    // Do a fun animation while speaking
    _startCallingAnimation();

    // Stop speaking after the audio finishes (estimate 3-4 seconds)
    _speechTimer?.cancel();
    _speechTimer = Timer(const Duration(seconds: 4), () {
      _chefBob.stopSpeaking();
      _notifyStateChanged();
    });
  }

  // Start Chef Bob's calling animation
  void _startCallingAnimation() {
    _animationTimer?.cancel();

    // Cycle through calling animations
    int animationStep = 0;
    _animationTimer = Timer.periodic(const Duration(milliseconds: 800), (timer) {
      switch (animationStep % 3) {
        case 0:
          _chefBob.currentAnimation = ChefBobAnimation.calling;
          break;
        case 1:
          _chefBob.currentAnimation = ChefBobAnimation.tossing;
          break;
        case 2:
          _chefBob.currentAnimation = ChefBobAnimation.pointing;
          break;
      }

      _notifyStateChanged();
      animationStep++;

      // Stop after a few cycles
      if (animationStep >= 6) {
        timer.cancel();
        _chefBob.currentAnimation = ChefBobAnimation.idle;
        _notifyStateChanged();
      }
    });
  }

  // Make Chef Bob encourage the player (when close to winning)
  Future<void> encouragePlayer() async {
    _chefBob.encourage();
    _notifyStateChanged();

    // Play encouragement audio (could be a generic encouraging sound)
    await _audioService.playGenericCall();

    _notifyGameEvent('player_encouraged', {
      'message': _chefBob.currentMessage,
    });

    // Stop speaking after a moment
    _speechTimer?.cancel();
    _speechTimer = Timer(const Duration(seconds: 3), () {
      _chefBob.stopSpeaking();
      _notifyStateChanged();
    });
  }

  // Make Chef Bob celebrate a win
  Future<void> celebrateWin() async {
    _chefBob.celebrate();
    _notifyStateChanged();

    // Play celebration audio
    await _audioService.playCelebration();
    await _audioService.playWin();

    _notifyGameEvent('win_celebrated', {
      'message': _chefBob.currentMessage,
    });

    // Celebration animation
    _startCelebrationAnimation();

    // Stop celebrating after a while
    _speechTimer?.cancel();
    _speechTimer = Timer(const Duration(seconds: 5), () {
      _chefBob.stopSpeaking();
      _notifyStateChanged();
    });
  }

  // Start Chef Bob's celebration animation
  void _startCelebrationAnimation() {
    _animationTimer?.cancel();

    // Energetic celebration animation
    int animationStep = 0;
    _animationTimer = Timer.periodic(const Duration(milliseconds: 600), (timer) {
      switch (animationStep % 4) {
        case 0:
          _chefBob.currentAnimation = ChefBobAnimation.celebrating;
          break;
        case 1:
          _chefBob.currentAnimation = ChefBobAnimation.waving;
          break;
        case 2:
          _chefBob.currentAnimation = ChefBobAnimation.tossing;
          break;
        case 3:
          _chefBob.currentAnimation = ChefBobAnimation.pointing;
          break;
      }

      _notifyStateChanged();
      animationStep++;

      // Stop after many cycles (longer celebration)
      if (animationStep >= 12) {
        timer.cancel();
        _chefBob.currentAnimation = ChefBobAnimation.idle;
        _notifyStateChanged();
      }
    });
  }

  // Make Chef Bob console the player (no win)
  Future<void> consolePlayer() async {
    _chefBob.console();
    _notifyStateChanged();

    // Play consolation audio
    await _audioService.playGenericCall();

    _notifyGameEvent('player_consoled', {
      'message': _chefBob.currentMessage,
    });

    // Stop speaking after a moment
    _speechTimer?.cancel();
    _speechTimer = Timer(const Duration(seconds: 4), () {
      _chefBob.stopSpeaking();
      _notifyStateChanged();
    });
  }

  // Stop all Chef Bob activities
  void stopCalling() {
    _animationTimer?.cancel();
    _speechTimer?.cancel();
    _chefBob.stopSpeaking();
    _audioService.stopAll();
    _notifyStateChanged();

    _notifyGameEvent('calling_stopped', {});
  }

  // Hide Chef Bob
  void hideChefBob() {
    _chefBob.hide();
    stopCalling();
    _notifyStateChanged();
  }

  // Show Chef Bob
  void showChefBob() {
    _chefBob.show();
    _notifyStateChanged();
  }

  // Reset Chef Bob to initial state
  void reset() {
    stopCalling();
    _chefBob.reset();
    _notifyStateChanged();
  }

  // Set callback for Chef Bob state changes
  void setOnChefBobStateChanged(OnChefBobStateChanged callback) {
    onChefBobStateChanged = callback;
  }

  // Set callback for symbol calls
  void setOnSymbolCalled(OnSymbolCalled callback) {
    onSymbolCalled = callback;
  }

  // Set callback for game events
  void setOnGameEvent(OnGameEvent callback) {
    onGameEvent = callback;
  }

  // Notify UI about Chef Bob state changes
  void _notifyStateChanged() {
    onChefBobStateChanged?.call(_chefBob);
  }

  // Notify UI about game events
  void _notifyGameEvent(String event, Map<String, dynamic> data) {
    onGameEvent?.call(event, data);
  }

  // Get current Chef Bob state for UI
  Map<String, dynamic> getCurrentState() {
    return _chefBob.getCurrentState();
  }

  // Check if Chef Bob is currently speaking
  bool get isSpeaking => _chefBob.isSpeaking;

  // Check if Chef Bob is visible
  bool get isVisible => _chefBob.isVisible;

  // Get current message
  String get currentMessage => _chefBob.currentMessage;

  // Get current mood
  ChefBobMood get currentMood => _chefBob.currentMood;

  // Get current animation
  ChefBobAnimation get currentAnimation => _chefBob.currentAnimation;

  // Dispose of resources
  void dispose() {
    _animationTimer?.cancel();
    _speechTimer?.cancel();
    _audioService.dispose();
  }
}