// 📅 Daily Plays Model
// This tracks daily play limits and extra play purchases

import 'bingo_card.dart';

// Daily play tracking for each game mode
class DailyPlays {
  final String userId;
  final DateTime date; // Date for this tracking (YYYY-MM-DD)

  // Free plays used today (1 per game mode per day)
  int scratch3x3FreePlays;
  int bingo5x5FreePlays;
  int bingo8x8FreePlays;

  // Extra plays purchased with points
  int scratch3x3ExtraPlays;
  int bingo5x5ExtraPlays;
  int bingo8x8ExtraPlays;

  // Points spent on extra plays today
  int pointsSpentToday;

  DailyPlays({
    required this.userId,
    required this.date,
    this.scratch3x3FreePlays = 0,
    this.bingo5x5FreePlays = 0,
    this.bingo8x8FreePlays = 0,
    this.scratch3x3ExtraPlays = 0,
    this.bingo5x5ExtraPlays = 0,
    this.bingo8x8ExtraPlays = 0,
    this.pointsSpentToday = 0,
  });

  // Get total plays for a specific game mode
  int getTotalPlays(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        return scratch3x3FreePlays + scratch3x3ExtraPlays;
      case BingoCardType.small5x5:
        return bingo5x5FreePlays + bingo5x5ExtraPlays;
      case BingoCardType.large8x8:
        return bingo8x8FreePlays + bingo8x8ExtraPlays;
    }
  }

  // Check if user can play for free
  bool canPlayFree(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        return scratch3x3FreePlays < 1;
      case BingoCardType.small5x5:
        return bingo5x5FreePlays < 1;
      case BingoCardType.large8x8:
        return bingo8x8FreePlays < 1;
    }
  }

  // Record a free play
  void recordFreePlay(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        scratch3x3FreePlays++;
        break;
      case BingoCardType.small5x5:
        bingo5x5FreePlays++;
        break;
      case BingoCardType.large8x8:
        bingo8x8FreePlays++;
        break;
    }
  }

  // Record an extra play purchase
  void recordExtraPlay(BingoCardType cardType, int pointsCost) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        scratch3x3ExtraPlays++;
        break;
      case BingoCardType.small5x5:
        bingo5x5ExtraPlays++;
        break;
      case BingoCardType.large8x8:
        bingo8x8ExtraPlays++;
        break;
    }
    pointsSpentToday += pointsCost;
  }

  // Get the cost for an extra play (different for each game mode)
  static int getExtraPlayCost(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        return 50; // 50 points for extra scratch card
      case BingoCardType.small5x5:
        return 30; // 30 points for extra 5x5 bingo
      case BingoCardType.large8x8:
        return 75; // 75 points for extra 8x8 bingo
    }
  }

  // Get game mode display name
  static String getGameModeName(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.scratch3x3:
        return '3x3 Scratch Card';
      case BingoCardType.small5x5:
        return '5x5 Quick Game';
      case BingoCardType.large8x8:
        return '8x8 Challenge';
    }
  }

  // Convert to/from JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'date': date.toIso8601String().split('T')[0], // Store as YYYY-MM-DD
      'scratch3x3FreePlays': scratch3x3FreePlays,
      'bingo5x5FreePlays': bingo5x5FreePlays,
      'bingo8x8FreePlays': bingo8x8FreePlays,
      'scratch3x3ExtraPlays': scratch3x3ExtraPlays,
      'bingo5x5ExtraPlays': bingo5x5ExtraPlays,
      'bingo8x8ExtraPlays': bingo8x8ExtraPlays,
      'pointsSpentToday': pointsSpentToday,
    };
  }

  factory DailyPlays.fromJson(Map<String, dynamic> json) {
    return DailyPlays(
      userId: json['userId'] ?? '',
      date: DateTime.parse(json['date'] ?? DateTime.now().toIso8601String()),
      scratch3x3FreePlays: json['scratch3x3FreePlays'] ?? 0,
      bingo5x5FreePlays: json['bingo5x5FreePlays'] ?? 0,
      bingo8x8FreePlays: json['bingo8x8FreePlays'] ?? 0,
      scratch3x3ExtraPlays: json['scratch3x3ExtraPlays'] ?? 0,
      bingo5x5ExtraPlays: json['bingo5x5ExtraPlays'] ?? 0,
      bingo8x8ExtraPlays: json['bingo8x8ExtraPlays'] ?? 0,
      pointsSpentToday: json['pointsSpentToday'] ?? 0,
    );
  }

  // Create a new daily plays record for today
  factory DailyPlays.forToday(String userId) {
    return DailyPlays(
      userId: userId,
      date: DateTime.now(),
    );
  }

  // Check if this record is for today
  bool get isToday {
    final today = DateTime.now();
    return date.year == today.year &&
           date.month == today.month &&
           date.day == today.day;
  }

  @override
  String toString() {
    return 'DailyPlays(userId: $userId, date: ${date.toIso8601String().split('T')[0]}, totalPlays: ${getTotalPlays(BingoCardType.scratch3x3) + getTotalPlays(BingoCardType.small5x5) + getTotalPlays(BingoCardType.large8x8)})';
  }
}