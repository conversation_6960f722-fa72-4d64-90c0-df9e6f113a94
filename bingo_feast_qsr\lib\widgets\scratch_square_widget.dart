// 🎫 Scratch Square Widget
// This creates a scratchable surface for revealing food symbols

import 'package:flutter/material.dart';
import '../models/bingo_card.dart';
import '../services/audio_service.dart';

class ScratchSquareWidget extends StatefulWidget {
  final BingoSquare square;
  final int row;
  final int col;
  final bool isCenterFree;
  final VoidCallback? onRevealed;
  final double size;

  const ScratchSquareWidget({
    super.key,
    required this.square,
    required this.row,
    required this.col,
    this.isCenterFree = false,
    this.onRevealed,
    this.size = 80,
  });

  @override
  State<ScratchSquareWidget> createState() => _ScratchSquareWidgetState();
}

class _ScratchSquareWidgetState extends State<ScratchSquareWidget>
    with SingleTickerProviderStateMixin {
  final List<Offset> _scratchedPoints = [];
  final AudioService _audioService = AudioService();
  late AnimationController _revealController;
  late Animation<double> _revealAnimation;

  bool _isScratching = false;
  double _scratchedPercentage = 0.0;
  static const double _revealThreshold = 0.3; // 30% scratched to reveal

  @override
  void initState() {
    super.initState();
    _revealController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _revealAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _revealController,
      curve: Curves.easeOutBack,
    ));
  }

  @override
  void dispose() {
    _revealController.dispose();
    super.dispose();
  }

  void _onPanStart(DragStartDetails details) {
    if (widget.square.isRevealed || widget.isCenterFree) return;

    setState(() {
      _isScratching = true;
    });

    _addScratchPoint(details.localPosition);
  }

  void _onPanUpdate(DragUpdateDetails details) {
    if (widget.square.isRevealed || widget.isCenterFree) return;

    _addScratchPoint(details.localPosition);
  }

  void _onPanEnd(DragEndDetails details) {
    setState(() {
      _isScratching = false;
    });
  }

  void _addScratchPoint(Offset point) {
    setState(() {
      _scratchedPoints.add(point);
    });

    // Play scratch sound occasionally
    if (_scratchedPoints.length % 3 == 0) {
      _audioService.playScratch();
    }

    // Calculate scratched percentage
    _calculateScratchedPercentage();

    // Auto-reveal if enough is scratched
    if (_scratchedPercentage >= _revealThreshold && !widget.square.isRevealed) {
      _revealSquare();
    }
  }

  void _calculateScratchedPercentage() {
    if (_scratchedPoints.isEmpty) {
      _scratchedPercentage = 0.0;
      return;
    }

    // Simple calculation based on number of scratch points
    // In a real implementation, you'd calculate actual area coverage
    final maxPoints = (widget.size * widget.size) / 100; // Rough estimate
    _scratchedPercentage = (_scratchedPoints.length / maxPoints).clamp(0.0, 1.0);
  }

  void _revealSquare() {
    if (widget.square.isRevealed) return;

    setState(() {
      widget.square.isRevealed = true;
      widget.square.isMarked = true;
    });

    _revealController.forward();
    _audioService.playCardMark();
    widget.onRevealed?.call();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onPanStart: _onPanStart,
      onPanUpdate: _onPanUpdate,
      onPanEnd: _onPanEnd,
      onTap: () {
        // Allow tap to reveal for accessibility
        if (!widget.square.isRevealed && !widget.isCenterFree) {
          _revealSquare();
        }
      },
      child: Container(
        width: widget.size,
        height: widget.size,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: widget.square.isMarked
              ? Colors.orange.shade600
              : Colors.grey.shade300,
            width: widget.square.isMarked ? 3 : 1,
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Stack(
            children: [
              // Revealed content (food symbol)
              _buildRevealedContent(),

              // Scratch overlay
              if (!widget.square.isRevealed && !widget.isCenterFree)
                _buildScratchOverlay(),

              // Reveal animation overlay
              if (widget.square.isRevealed)
                _buildRevealAnimation(),
            ],
          ),
        ),
      ),
    );
  }

  // Build the revealed content (food symbol)
  Widget _buildRevealedContent() {
    return Container(
      width: widget.size,
      height: widget.size,
      color: widget.square.isMarked
        ? Colors.orange.shade300
        : widget.isCenterFree
          ? Colors.orange.shade100
          : Colors.grey.shade100,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (widget.isCenterFree)
            const Text(
              'FREE',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: Colors.orange,
              ),
            )
          else ...[
            Icon(
              Icons.restaurant,
              size: 20,
              color: widget.square.isMarked ? Colors.white : Colors.grey.shade600,
            ),
            const SizedBox(height: 2),
            Text(
              widget.square.symbol.name,
              style: TextStyle(
                fontSize: 10,
                fontWeight: FontWeight.bold,
                color: widget.square.isMarked ? Colors.white : Colors.grey.shade800,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
          if (widget.square.isMarked)
            const Icon(
              Icons.check_circle,
              size: 16,
              color: Colors.white,
            ),
        ],
      ),
    );
  }

  // Build the scratch overlay
  Widget _buildScratchOverlay() {
    return CustomPaint(
      size: Size(widget.size, widget.size),
      painter: ScratchOverlayPainter(
        scratchedPoints: _scratchedPoints,
        isScratching: _isScratching,
      ),
    );
  }

  // Build the reveal animation
  Widget _buildRevealAnimation() {
    return AnimatedBuilder(
      animation: _revealAnimation,
      builder: (context, child) {
        return Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            color: Colors.orange.withValues(alpha: 0.3 * (1 - _revealAnimation.value)),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Center(
            child: Transform.scale(
              scale: _revealAnimation.value,
              child: const Icon(
                Icons.star,
                color: Colors.orange,
                size: 24,
              ),
            ),
          ),
        );
      },
    );
  }
}

// Custom painter for the scratch overlay
class ScratchOverlayPainter extends CustomPainter {
  final List<Offset> scratchedPoints;
  final bool isScratching;

  ScratchOverlayPainter({
    required this.scratchedPoints,
    required this.isScratching,
  });

  @override
  void paint(Canvas canvas, Size size) {
    // Draw the scratch-off surface
    final scratchPaint = Paint()
      ..color = Colors.grey.shade400
      ..style = PaintingStyle.fill;

    // Draw base scratch surface
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), scratchPaint);

    // Draw scratch pattern texture
    final texturePaint = Paint()
      ..color = Colors.grey.shade500
      ..strokeWidth = 1;

    for (int i = 0; i < size.width; i += 4) {
      for (int j = 0; j < size.height; j += 4) {
        if ((i + j) % 8 == 0) {
          canvas.drawCircle(Offset(i.toDouble(), j.toDouble()), 0.5, texturePaint);
        }
      }
    }

    // Draw "SCRATCH" text
    final textPainter = TextPainter(
      text: const TextSpan(
        text: 'SCRATCH',
        style: TextStyle(
          color: Colors.white,
          fontSize: 10,
          fontWeight: FontWeight.bold,
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );

    // Draw scratched areas (holes in the overlay)
    if (scratchedPoints.isNotEmpty) {
      final scratchedPaint = Paint()
        ..color = Colors.transparent
        ..blendMode = BlendMode.clear
        ..strokeWidth = 8
        ..strokeCap = StrokeCap.round;

      for (int i = 0; i < scratchedPoints.length - 1; i++) {
        canvas.drawLine(scratchedPoints[i], scratchedPoints[i + 1], scratchedPaint);
      }

      // Draw individual scratch points
      for (final point in scratchedPoints) {
        canvas.drawCircle(point, 4, scratchedPaint);
      }
    }

    // Add scratching effect when actively scratching
    if (isScratching && scratchedPoints.isNotEmpty) {
      final glowPaint = Paint()
        ..color = Colors.orange.withValues(alpha: 0.3)
        ..strokeWidth = 12
        ..strokeCap = StrokeCap.round;

      final lastPoint = scratchedPoints.last;
      canvas.drawCircle(lastPoint, 6, glowPaint);
    }
  }

  @override
  bool shouldRepaint(ScratchOverlayPainter oldDelegate) {
    return scratchedPoints != oldDelegate.scratchedPoints ||
           isScratching != oldDelegate.isScratching;
  }
}