// This is a basic Flutter widget test.
//
// To perform an interaction with a widget in your test, use the WidgetTester
// utility in the flutter_test package. For example, you can send tap and scroll
// gestures. You can also use WidgetTester to find child widgets in the widget
// tree, read text, and verify that the values of widget properties are correct.

import 'package:flutter_test/flutter_test.dart';

import 'package:bingo_feast_qsr/main.dart';

void main() {
  testWidgets('Bingo app loads correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const BingoFeastApp());

    // Verify that the app title appears
    expect(find.text('Welcome to Bingo Feast!'), findsOneWidget);

    // Verify that game mode buttons appear
    expect(find.text('5x5 Quick Game'), findsOneWidget);
    expect(find.text('5x5 Scratch Mode'), findsOneWidget);
    expect(find.text('8x8 Challenge'), findsOneWidget);
  });
}
