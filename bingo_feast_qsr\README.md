# 🎮 Hatch 'n' Wing Bingo Feast

A fun bingo game for QSR (Quick Service Restaurant) mobile apps! Players earn bingo cards through purchases and play with a virtual caller (<PERSON>) to win rewards like free food and discounts.

## 🚀 For Young Developers - Setup Guide

Hi! Let's set up everything you need to build your awesome bingo game!

### What We Just Created
This is a Flutter project - think of it like building the foundation of a house before we add the rooms (game features).

### Step 1: Check if Flutter is Installed
Open your terminal (Command Prompt on Windows or Terminal on Mac) and type:
```bash
flutter doctor
```

If you see green checkmarks, you're ready! If not, you need to install Flutter first.

### Step 2: Install Required Software (if needed)

**Flutter SDK:**
- Windows: https://flutter.dev/docs/get-started/install/windows
- Mac: https://flutter.dev/docs/get-started/install/macos

**Visual Studio Code:**
1. Download from https://code.visualstudio.com/
2. Install these extensions:
   - Flutter (by Dart Code)
   - Dart (by Dart Code)
   - Firebase (by Firebase)

### Step 3: Open Your Project
1. Open Visual Studio Code
2. Click "File" → "Open Folder"
3. Select the `bingo_feast_qsr` folder
4. Click "Select Folder"

### Step 4: Test Your Setup
In VS Code terminal (View → Terminal), run:
```bash
flutter run
```

You should see a simple counter app - that means Flutter is working!

## 🎯 Game Features We're Building

- **5x5 Bingo Cards** - Quick 2-3 minute games
- **8x8 Challenge Cards** - Longer 4-6 minute games
- **Chef Bob Virtual Caller** - Calls out food symbols
- **Scratch Mode** - Swipe to reveal symbols
- **AR Features** - 3D Chef Bob and food animations
- **Rewards System** - Free food, discounts, loyalty points
- **QSR App Integration** - Works with existing restaurant app

## 📁 Project Structure

- `lib/` - All your game code goes here!
- `lib/screens/` - Different game screens
- `lib/models/` - Data structures (bingo cards, players, etc.)
- `lib/services/` - Firebase and game logic
- `android/` & `ios/` - Platform-specific settings

## 🔥 Next Steps

Now we'll start building your bingo game! I'll create all the code files and explain what each one does in simple terms.

## 🆘 Need Help?

If anything doesn't work, ask an adult to help with installation. The most important thing is getting Flutter and VS Code working together!
