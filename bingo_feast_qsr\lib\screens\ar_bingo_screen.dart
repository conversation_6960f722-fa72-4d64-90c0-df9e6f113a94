// 🥽 AR Bingo Screen
// Augmented Reality-style mode with camera and 3D overlays

import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

import '../models/bingo_card.dart';
import '../models/game_state.dart';
import '../models/food_symbol.dart';
import '../services/virtual_caller_service.dart';
import '../services/audio_service.dart';
import '../widgets/food_image_widget.dart';

// Simple floating food symbol for AR effect
class FloatingFoodSymbol {
  final FoodSymbol symbol;
  final Offset position;
  final double scale;
  final double rotation;
  final DateTime createdAt;

  FloatingFoodSymbol({
    required this.symbol,
    required this.position,
    this.scale = 1.0,
    this.rotation = 0.0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}

// AR Grid painter for web fallback
class ARGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withValues(alpha: 0.2)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw grid lines
    const spacing = 50.0;

    // Vertical lines
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ARBingoScreen extends StatefulWidget {
  final BingoCard bingoCard;

  const ARBingoScreen({
    super.key,
    required this.bingoCard,
  });

  @override
  State<ARBingoScreen> createState() => _ARBingoScreenState();
}

class _ARBingoScreenState extends State<ARBingoScreen>
    with TickerProviderStateMixin {
  // Camera
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  // Game State
  late GameState _gameState;
  late VirtualCallerService _callerService;
  late AudioService _audioService;

  // AR-style State
  bool _isCameraInitialized = false;
  bool _isChefBobPlaced = false;
  bool _isGameStarted = false;

  // Animation Controllers for 3D effects
  late AnimationController _chefBobAnimationController;
  late AnimationController _foodFloatController;
  late AnimationController _particleController;
  late AnimationController _soundWaveController;
  late Animation<double> _chefBobBounce;
  late Animation<double> _foodFloat;
  late Animation<double> _particleAnimation;
  late Animation<double> _soundWaveAnimation;

  // Floating Food Symbols
  final List<FloatingFoodSymbol> _floatingFoods = [];

  // UI State
  FoodSymbol? _lastCalledSymbol;
  String _arInstructions = "Tap to place Chef Bob in your space!";
  bool _chefBobTapped = false;
  String _gameInstructions = "Tap Chef Bob to call the next food!";
  int _chefBobTapCount = 0; // Debug counter

  @override
  void initState() {
    super.initState();
    _initializeGame();
    _initializeCamera();
    _setupAnimations();
  }

  void _initializeGame() {
    _gameState = GameState(
      gameId: 'ar_game_${DateTime.now().millisecondsSinceEpoch}',
      card: widget.bingoCard,
      mode: GameMode.ar,
    );
    _callerService = VirtualCallerService();
    _audioService = AudioService();
  }

  void _initializeCamera() async {
    try {
      // For web, skip camera initialization and use fallback
      if (kIsWeb) {
        setState(() {
          _isCameraInitialized = true;
        });
        return;
      }

      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        _cameraController = CameraController(
          _cameras![0],
          ResolutionPreset.medium,
        );
        await _cameraController!.initialize();
        if (mounted) {
          setState(() {
            _isCameraInitialized = true;
          });
        }
      }
    } catch (e) {
      debugPrint('Error initializing camera: $e');
      // Fallback: Set camera as initialized anyway
      setState(() {
        _isCameraInitialized = true;
      });
    }
  }

  void _setupAnimations() {
    _chefBobAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _foodFloatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _soundWaveController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _chefBobBounce = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _chefBobAnimationController,
      curve: Curves.easeInOut,
    ));

    _foodFloat = Tween<double>(
      begin: 0.0,
      end: 20.0,
    ).animate(CurvedAnimation(
      parent: _foodFloatController,
      curve: Curves.easeInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));

    _soundWaveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _soundWaveController,
      curve: Curves.easeOut,
    ));

    _chefBobAnimationController.repeat(reverse: true);
    _foodFloatController.repeat(reverse: true);
    _particleController.repeat();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Fixed AR Camera Section (Top)
          Container(
            height: 250,
            width: double.infinity,
            child: Stack(
              children: [
                // Camera Background or Fallback
                if (_isCameraInitialized && _cameraController != null && !kIsWeb)
                  Positioned.fill(
                    child: CameraPreview(_cameraController!),
                  )
                else if (_isCameraInitialized && kIsWeb)
                  // Web fallback - gradient background simulating AR space
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.blue.shade900,
                          Colors.purple.shade900,
                          Colors.black,
                        ],
                      ),
                    ),
                    child: Stack(
                      children: [
                        // Simulated AR grid pattern
                        CustomPaint(
                          size: Size.infinite,
                          painter: ARGridPainter(),
                        ),
                        Center(
                          child: Text(
                            '🥽 AR Simulation Mode\n(Camera not available on web)',
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.7),
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                else
                  Container(
                    color: Colors.black,
                    child: const Center(
                      child: CircularProgressIndicator(color: Colors.white),
                    ),
                  ),

                // 3D Chef Bob (when placed)
                if (_isChefBobPlaced) _build3DChefBob(),

                // Particle effects around Chef Bob
                if (_isChefBobPlaced) _buildParticleEffects(),

                // Sound wave effects when calling
                if (_isGameStarted && _lastCalledSymbol != null) _buildSoundWaveEffect(),

                // Floating Food Symbols
                ..._floatingFoods.map((food) => _buildFloatingFood(food)),

                // AR Instructions Overlay
                if (!_isGameStarted) _buildARInstructions(),

                // Game UI Overlay
                if (_isGameStarted) _buildGameOverlay(),

                // Debug Test Button
                if (_isGameStarted) _buildDebugTestButton(),

                // Back Button
                Positioned(
                  top: 20,
                  left: 20,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.black.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Scrollable Content Section (Bottom)
          Expanded(
            child: Container(
              color: Colors.grey.shade100,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    // Chef Bob Caller Section
                    if (_isGameStarted) _buildChefBobCallerSection(),

                    const SizedBox(height: 16),

                    // Full Bingo Card
                    if (_isGameStarted) _buildFullBingoCard(),

                    const SizedBox(height: 100), // Extra space at bottom
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _build3DChefBob() {
    return AnimatedBuilder(
      animation: _chefBobBounce,
      builder: (context, child) {
        return Positioned(
          bottom: 280 + _chefBobBounce.value, // Higher position
          left: MediaQuery.of(context).size.width * 0.5 - 50,
          child: GestureDetector(
            onTap: () {
              debugPrint('🎯 Chef Bob tapped!');
              debugPrint('🎮 Game started: $_isGameStarted');
              debugPrint('🎯 Game status: ${_gameState.status}');
              debugPrint('📋 Calling sequence length: ${_gameState.callingSequence.length}');
              debugPrint('🔢 Current call index: ${_gameState.currentCallIndex}');

              // Ensure game is started
              if (!_isGameStarted) {
                debugPrint('⚠️ Game not started yet - starting now');
                _startARGame();
                return;
              }

              // Visual feedback
              setState(() {
                _chefBobTapped = true;
                _chefBobTapCount++; // Increment tap counter
              });

              // Reset visual feedback after animation
              Future.delayed(const Duration(milliseconds: 200), () {
                setState(() {
                  _chefBobTapped = false;
                });
              });

              // Call the next food in sequence
              _callNextSymbol();
            },
            child: AnimatedContainer(
            duration: const Duration(milliseconds: 200),
            width: 100,
            height: 120,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: _chefBobTapped ? [
                  Colors.yellow.shade300,
                  Colors.orange.shade700,
                ] : [
                  Colors.orange.shade300,
                  Colors.orange.shade600,
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: _chefBobTapped ? Border.all(
                color: Colors.yellow.shade400,
                width: 3,
              ) : null,
              boxShadow: [
                BoxShadow(
                  color: _chefBobTapped
                      ? Colors.yellow.withValues(alpha: 0.5)
                      : Colors.black.withValues(alpha: 0.3),
                  blurRadius: _chefBobTapped ? 15 : 10,
                  offset: const Offset(0, 5),
                  spreadRadius: _chefBobTapped ? 2 : 0,
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Chef Hat
                Container(
                  width: 40,
                  height: 30,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: const Icon(
                    Icons.restaurant_menu,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(height: 8),
                // Chef Face
                const Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 32,
                ),
                const SizedBox(height: 4),
                // Chef Name
                const Text(
                  'Chef Bob',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    },
  );
}

  Widget _buildParticleEffects() {
    return AnimatedBuilder(
      animation: _particleAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 280, // Match Chef Bob's position
          left: MediaQuery.of(context).size.width * 0.5 - 75,
          child: SizedBox(
            width: 150,
            height: 150,
            child: Stack(
              children: List.generate(8, (index) {
                final angle = (index * 45.0) * (3.14159 / 180.0);
                final radius = 50.0 + (_particleAnimation.value * 30.0);
                final x = 75 + (radius * cos(angle));
                final y = 75 + (radius * sin(angle));

                return Positioned(
                  left: x - 4,
                  top: y - 4,
                  child: Opacity(
                    opacity: (1.0 - _particleAnimation.value) * 0.8,
                    child: Container(
                      width: 8,
                      height: 8,
                      decoration: BoxDecoration(
                        color: Colors.orange.shade300,
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withValues(alpha: 0.5),
                            blurRadius: 4,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSoundWaveEffect() {
    return AnimatedBuilder(
      animation: _soundWaveAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 330, // Above Chef Bob
          left: MediaQuery.of(context).size.width * 0.5 - 50,
          child: SizedBox(
            width: 100,
            height: 100,
            child: Stack(
              children: List.generate(3, (index) {
                final scale = 0.3 + (index * 0.3) + (_soundWaveAnimation.value * 0.7);
                final opacity = (1.0 - _soundWaveAnimation.value) * (1.0 - index * 0.2);

                return Center(
                  child: Transform.scale(
                    scale: scale,
                    child: Opacity(
                      opacity: opacity,
                      child: Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.cyan.shade300,
                            width: 2,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Widget _buildFloatingFood(FloatingFoodSymbol food) {
    final age = DateTime.now().difference(food.createdAt).inSeconds;
    final opacity = (1.0 - (age / 10.0)).clamp(0.0, 1.0);
    final pulseScale = 1.0 + (sin(_foodFloat.value * 2 * pi) * 0.1);

    return AnimatedBuilder(
      animation: _foodFloat,
      builder: (context, child) {
        return Positioned(
          left: food.position.dx,
          top: food.position.dy - _foodFloat.value,
          child: Transform.rotate(
            angle: food.rotation + (_foodFloat.value * 0.1),
            child: Transform.scale(
              scale: food.scale * pulseScale,
              child: Opacity(
                opacity: opacity,
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.white.withValues(alpha: 0.95),
                        Colors.blue.shade50.withValues(alpha: 0.9),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: Colors.cyan.shade300.withValues(alpha: 0.6),
                      width: 2,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.cyan.withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                        spreadRadius: 2,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FoodImageWidget(
                        symbol: food.symbol,
                        size: 40,
                        isMarked: false,
                        isRevealed: true,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        food.symbol.name,
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildARInstructions() {
    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: GestureDetector(
        onTap: _isCameraInitialized && !_isChefBobPlaced ? _placeChefBob : null,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.black.withValues(alpha: 0.8),
                Colors.black.withValues(alpha: 0.6),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                _isCameraInitialized ? Icons.touch_app : Icons.camera_alt,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(height: 12),
              Text(
                _arInstructions,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              if (_isCameraInitialized && !_isChefBobPlaced) ...[
                const SizedBox(height: 16),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  decoration: BoxDecoration(
                    color: Colors.orange,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Text(
                    'Tap to Place Chef Bob',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGameOverlay() {
    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withValues(alpha: 0.9),
              Colors.white.withValues(alpha: 0.7),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Progress Bar
            Row(
              children: [
                const Icon(Icons.timer, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress: ${(_gameState.progress * 100).toInt()}%',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: _gameState.progress,
                        backgroundColor: Colors.grey.shade300,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                        minHeight: 6,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Last Called Symbol
            if (_lastCalledSymbol != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade200, Colors.orange.shade300],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FoodImageWidget(
                      symbol: _lastCalledSymbol!,
                      size: 24,
                      isMarked: false,
                      isRevealed: true,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Chef Bob called: ${_lastCalledSymbol!.name}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDebugTestButton() {
    return Positioned(
      top: 60,
      right: 20,
      child: Column(
        children: [
          FloatingActionButton(
            mini: true,
            onPressed: () {
              // Test floating food creation
              final testSymbol = FoodSymbols.allSymbols.first;
              debugPrint('Manual test: Creating floating food for ${testSymbol.name}');
              _createFloatingFoodSymbol(testSymbol);
            },
            backgroundColor: Colors.red.withValues(alpha: 0.8),
            child: const Icon(Icons.bug_report, color: Colors.white, size: 16),
          ),
          const SizedBox(height: 8),
          // Debug info display
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Started: $_isGameStarted',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Chef: $_isChefBobPlaced',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Status: ${_gameState.status}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Taps: $_chefBobTapCount',
                  style: const TextStyle(color: Colors.yellow, fontSize: 10),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Test button for calling
          ElevatedButton(
            onPressed: () {
              debugPrint('🧪 TEST BUTTON: Calling _callNextSymbol directly');
              setState(() {
                _chefBobTapCount++;
              });
              if (!_isGameStarted) {
                _startARGame();
              } else {
                _callNextSymbol();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              minimumSize: const Size(60, 30),
            ),
            child: const Text(
              'TEST\nCALL',
              style: TextStyle(fontSize: 8, color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  void _placeChefBob() {
    if (_isChefBobPlaced) return;

    setState(() {
      _isChefBobPlaced = true;
      _arInstructions = "Perfect! Chef Bob is ready to call bingo!";
    });

    // Start the game immediately for testing
    _startARGame();
  }

  Widget _buildChefBobCallerSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade100, Colors.orange.shade200],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '👨‍🍳 Chef Bob\'s Kitchen',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 12),

          // Large Chef Bob Button
          GestureDetector(
            onTap: () {
              debugPrint('🎯 Large Chef Bob tapped!');
              setState(() {
                _chefBobTapCount++;
              });
              if (!_isGameStarted) {
                _startARGame();
              } else {
                _callNextSymbol();
              }
            },
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade300, Colors.orange.shade600],
                ),
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.restaurant_menu, color: Colors.white, size: 40),
                  SizedBox(height: 8),
                  Text(
                    'TAP TO\nCALL FOOD',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Game Status
          Text(
            'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.orange,
            ),
          ),

          // Last Called Food
          if (_lastCalledSymbol != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade300),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FoodImageWidget(
                    symbol: _lastCalledSymbol!,
                    size: 30,
                    isMarked: false,
                    isRevealed: true,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Last Called: ${_lastCalledSymbol!.name}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullBingoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '🎯 Your Bingo Card',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.blue,
            ),
          ),
          const SizedBox(height: 16),

          // Full 5x5 Bingo Grid
          AspectRatio(
            aspectRatio: 1.0,
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _gameState.card.gridSize,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: _gameState.card.gridSize * _gameState.card.gridSize,
              itemBuilder: (context, index) {
                final row = index ~/ _gameState.card.gridSize;
                final col = index % _gameState.card.gridSize;
                final square = _gameState.card.grid[row][col];

                return GestureDetector(
                  onTap: () {
                    // Allow manual marking of squares in AR mode
                    if (!square.isMarked) {
                      setState(() {
                        _gameState.card.markSquare(row, col, isCallerMode: true);
                      });

                      // Check for win
                      if (_gameState.card.checkForWin()) {
                        _gameState.hasWon = true;
                        _showWinDialog();
                      }
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      color: square.isMarked
                          ? Colors.green.withValues(alpha: 0.8)
                          : Colors.white,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: square.isMarked
                            ? Colors.green.shade600
                            : Colors.blue.shade300,
                        width: square.isMarked ? 3 : 2,
                      ),
                      boxShadow: square.isMarked ? [
                        BoxShadow(
                          color: Colors.green.withValues(alpha: 0.3),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ] : null,
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FoodImageWidget(
                          symbol: square.symbol,
                          size: 32,
                          isMarked: square.isMarked,
                          isRevealed: true, // Always show in AR mode
                        ),
                        const SizedBox(height: 4),
                        Text(
                          square.symbol.name,
                          style: TextStyle(
                            fontSize: 8,
                            fontWeight: FontWeight.bold,
                            color: square.isMarked ? Colors.white : Colors.black,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void _startARGame() async {
    setState(() {
      _isGameStarted = true;
    });

    // Reset game state
    _gameState.currentCallIndex = 0;
    _gameState.status = GameStatus.playing;
    _gameState.hasWon = false;

    // Generate calling sequence first
    _gameState.generateCallingSequence();
    _gameState.startGame();

    // Reveal all squares in AR mode (not a scratch game)
    for (int row = 0; row < _gameState.card.gridSize; row++) {
      for (int col = 0; col < _gameState.card.gridSize; col++) {
        _gameState.card.grid[row][col].isRevealed = true;
      }
    }

    // Debug: Check if calling sequence is generated
    debugPrint('AR Game Started!');
    debugPrint('Calling sequence length: ${_gameState.callingSequence.length}');
    debugPrint('Game status: ${_gameState.status}');
    debugPrint('All squares revealed for AR mode');

    await _callerService.greetPlayer();

    // Manual calling mode - user taps Chef Bob to call foods
    // No automatic calling
  }



  void _callNextSymbol() {
    debugPrint('🎯 _callNextSymbol called');
    debugPrint('📊 Current call index: ${_gameState.currentCallIndex}');
    debugPrint('📋 Calling sequence length: ${_gameState.callingSequence.length}');
    debugPrint('🎮 Game status: ${_gameState.status}');

    // Check if calling sequence is empty
    if (_gameState.callingSequence.isEmpty) {
      debugPrint('❌ ERROR: Calling sequence is empty! Generating now...');
      _gameState.generateCallingSequence();
      debugPrint('✅ Generated calling sequence with ${_gameState.callingSequence.length} symbols');
    }

    if (_gameState.currentCallIndex >= _gameState.callingSequence.length) {
      debugPrint('🏁 Game completed - no more symbols to call');
      return;
    }

    // Don't complete game until max calls reached
    if (_gameState.currentCallIndex >= _gameState.maxCalls) {
      debugPrint('🏁 Reached max calls - game completed');
      return;
    }

    final symbol = _gameState.callingSequence[_gameState.currentCallIndex];
    debugPrint('📢 Calling symbol: ${symbol.name}');
    debugPrint('🎯 Symbol index in sequence: ${_gameState.currentCallIndex}');

    // Update UI
    setState(() {
      _lastCalledSymbol = symbol;
    });

    // Make Chef Bob call it
    _callerService.callSymbol(symbol);

    // Trigger sound wave animation
    _soundWaveController.forward().then((_) {
      _soundWaveController.reset();
    });

    // Create floating 3D food symbol in AR
    _createFloatingFoodSymbol(symbol);
    debugPrint('Created floating food symbol for: ${symbol.name}');

    // Mark matching symbols on card
    final markedCount = _gameState.card.markSymbol(symbol);
    debugPrint('🎯 Marked ${markedCount} squares for ${symbol.name}');

    _gameState.currentCallIndex++;
    debugPrint('📊 Updated call index to: ${_gameState.currentCallIndex}');

    // Check for win
    if (_gameState.card.checkForWin()) {
      _gameState.hasWon = true;
      _callerService.celebrateWin();
      _showWinDialog();
    }
  }

  void _createFloatingFoodSymbol(FoodSymbol symbol) {
    final screenSize = MediaQuery.of(context).size;
    final random = Random();

    debugPrint('Creating floating food symbol for: ${symbol.name}');
    debugPrint('Screen size: ${screenSize.width} x ${screenSize.height}');

    // Create a floating food symbol at a random position
    final floatingFood = FloatingFoodSymbol(
      symbol: symbol,
      position: Offset(
        random.nextDouble() * (screenSize.width - 100) + 50,
        random.nextDouble() * 200 + 100, // Top area of screen
      ),
      scale: 0.8 + random.nextDouble() * 0.4, // Random scale 0.8-1.2
      rotation: random.nextDouble() * 6.28, // Random rotation
    );

    debugPrint('Floating food position: ${floatingFood.position}');
    debugPrint('Total floating foods before add: ${_floatingFoods.length}');

    setState(() {
      _floatingFoods.add(floatingFood);
    });

    debugPrint('Total floating foods after add: ${_floatingFoods.length}');

    // Remove the floating food after 10 seconds
    Timer(const Duration(seconds: 10), () {
      setState(() {
        _floatingFoods.remove(floatingFood);
      });
      debugPrint('Removed floating food: ${symbol.name}');
    });
  }

  void _showWinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 BINGO!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Congratulations! You won in AR mode!'),
            const SizedBox(height: 16),
            Text('Points Earned: ${_gameState.pointsEarned}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Return to main menu
            },
            child: const Text('Play Again'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _chefBobAnimationController.dispose();
    _foodFloatController.dispose();
    _particleController.dispose();
    _soundWaveController.dispose();
    _callerService.dispose();
    _audioService.dispose();
    super.dispose();
  }
}
