// 🥽 AR Bingo Screen
// Augmented Reality mode with 3D Chef Bob and floating food symbols

import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:ar_flutter_plugin/ar_flutter_plugin.dart';
import 'package:ar_flutter_plugin/datatypes/config_planedetection.dart';
import 'package:ar_flutter_plugin/datatypes/node_types.dart';
import 'package:ar_flutter_plugin/datatypes/hittest_result_types.dart';
import 'package:ar_flutter_plugin/managers/ar_location_manager.dart';
import 'package:ar_flutter_plugin/managers/ar_session_manager.dart';
import 'package:ar_flutter_plugin/managers/ar_object_manager.dart';
import 'package:ar_flutter_plugin/managers/ar_anchor_manager.dart';
import 'package:vector_math/vector_math_64.dart' as vector;

import '../models/bingo_card.dart';
import '../models/game_state.dart';
import '../models/food_symbol.dart';
import '../services/virtual_caller_service.dart';
import '../services/audio_service.dart';
import '../widgets/food_image_widget.dart';

class ARBingoScreen extends StatefulWidget {
  final BingoCard bingoCard;

  const ARBingoScreen({
    super.key,
    required this.bingoCard,
  });

  @override
  State<ARBingoScreen> createState() => _ARBingoScreenState();
}

class _ARBingoScreenState extends State<ARBingoScreen> {
  // AR Managers
  ARSessionManager? arSessionManager;
  ARObjectManager? arObjectManager;
  ARAnchorManager? arAnchorManager;
  ARLocationManager? arLocationManager;

  // Game State
  late GameState _gameState;
  late VirtualCallerService _callerService;
  late AudioService _audioService;
  
  // AR State
  bool _isARInitialized = false;
  bool _isPlaneDetected = false;
  bool _isChefBobPlaced = false;
  bool _isGameStarted = false;
  
  // AR Objects
  String? _chefBobNodeName;
  String? _bingoCardNodeName;
  final List<String> _foodSymbolNodes = [];
  
  // UI State
  FoodSymbol? _lastCalledSymbol;
  String _arInstructions = "Point your camera at a flat surface to place the bingo table";

  @override
  void initState() {
    super.initState();
    _initializeGame();
  }

  void _initializeGame() {
    _gameState = GameState(
      card: widget.bingoCard,
      mode: GameMode.ar,
    );
    _callerService = VirtualCallerService();
    _audioService = AudioService();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // AR View
          ARView(
            onARViewCreated: _onARViewCreated,
            planeDetectionConfig: PlaneDetectionConfig.horizontalAndVertical,
          ),
          
          // AR Instructions Overlay
          if (!_isGameStarted) _buildARInstructions(),
          
          // Game UI Overlay
          if (_isGameStarted) _buildGameOverlay(),
          
          // Back Button
          Positioned(
            top: 50,
            left: 20,
            child: Container(
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(25),
              ),
              child: IconButton(
                icon: const Icon(Icons.arrow_back, color: Colors.white),
                onPressed: () => Navigator.of(context).pop(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildARInstructions() {
    return Positioned(
      bottom: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.black.withValues(alpha: 0.8),
              Colors.black.withValues(alpha: 0.6),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.camera_alt,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 12),
            Text(
              _arInstructions,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            if (_isPlaneDetected && !_isChefBobPlaced) ...[
              const SizedBox(height: 16),
              ElevatedButton(
                onPressed: _placeChefBob,
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.orange,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                ),
                child: const Text('Place Chef Bob'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildGameOverlay() {
    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withValues(alpha: 0.9),
              Colors.white.withValues(alpha: 0.7),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          children: [
            // Progress Bar
            Row(
              children: [
                const Icon(Icons.timer, color: Colors.blue),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress: ${(_gameState.progress * 100).toInt()}%',
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
                      const SizedBox(height: 4),
                      LinearProgressIndicator(
                        value: _gameState.progress,
                        backgroundColor: Colors.grey.shade300,
                        valueColor: const AlwaysStoppedAnimation<Color>(Colors.blue),
                        minHeight: 6,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            
            // Last Called Symbol
            if (_lastCalledSymbol != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade200, Colors.orange.shade300],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FoodImageWidget(
                      symbol: _lastCalledSymbol!,
                      size: 24,
                      isMarked: false,
                      isRevealed: true,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Chef Bob called: ${_lastCalledSymbol!.name}',
                      style: const TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  void _onARViewCreated(
    ARSessionManager arSessionManager,
    ARObjectManager arObjectManager,
    ARAnchorManager arAnchorManager,
    ARLocationManager arLocationManager,
  ) {
    this.arSessionManager = arSessionManager;
    this.arObjectManager = arObjectManager;
    this.arAnchorManager = arAnchorManager;
    this.arLocationManager = arLocationManager;

    // Initialize AR session
    this.arSessionManager!.onInitialize(
      showFeaturePoints: false,
      showPlanes: true,
      customPlaneTexturePath: "assets/images/triangle.png",
      showWorldOrigin: false,
      handlePans: true,
      handleRotation: true,
    );

    // Listen for plane detection
    this.arSessionManager!.onPlaneOrPointTap = _onPlaneOrPointTapped;
    
    setState(() {
      _isARInitialized = true;
      _arInstructions = "Great! Now tap on a flat surface to place Chef Bob";
      _isPlaneDetected = true;
    });
  }

  void _onPlaneOrPointTapped(List<ARHitTestResult> hitTestResults) {
    if (!_isChefBobPlaced && hitTestResults.isNotEmpty) {
      _placeChefBobAtPosition(hitTestResults.first);
    }
  }

  void _placeChefBob() {
    // This will be called when user taps the "Place Chef Bob" button
    // For now, we'll place it at a default position
    _placeChefBobAtPosition(null);
  }

  void _placeChefBobAtPosition(ARHitTestResult? hitResult) async {
    if (_isChefBobPlaced) return;

    try {
      // Create Chef Bob 3D model (for now, we'll use a simple cube as placeholder)
      final chefBobNode = ARNode(
        type: NodeType.localGLTF2,
        uri: "assets/models/chef_bob.gltf", // We'll create this later
        scale: vector.Vector3(0.1, 0.1, 0.1),
        position: hitResult?.worldTransform.getTranslation() ?? vector.Vector3(0, 0, -1),
        rotation: vector.Vector4(0, 1, 0, 0),
      );

      // Add Chef Bob to AR scene
      final success = await arObjectManager!.addNode(chefBobNode);
      if (success) {
        _chefBobNodeName = chefBobNode.name;
        setState(() {
          _isChefBobPlaced = true;
          _arInstructions = "Perfect! Chef Bob is ready to call bingo!";
        });
        
        // Start the game after a short delay
        Future.delayed(const Duration(seconds: 2), () {
          _startARGame();
        });
      }
    } catch (e) {
      print('Error placing Chef Bob: $e');
      // Fallback: Start game without 3D model
      _startARGame();
    }
  }

  void _startARGame() async {
    setState(() {
      _isGameStarted = true;
    });
    
    _gameState.startGame();
    await _callerService.greetPlayer();
    
    // Start calling symbols
    _startCallerMode();
  }

  void _startCallerMode() {
    Timer.periodic(const Duration(seconds: 5), (timer) {
      if (_gameState.status != GameStatus.playing) {
        timer.cancel();
        return;
      }

      _callNextSymbol();
    });
  }

  void _callNextSymbol() {
    if (_gameState.currentCallIndex >= _gameState.callingSequence.length) {
      _gameState.status = GameStatus.completed;
      return;
    }

    final symbol = _gameState.callingSequence[_gameState.currentCallIndex];
    
    // Update UI
    setState(() {
      _lastCalledSymbol = symbol;
    });

    // Make Chef Bob call it
    _callerService.callSymbol(symbol);
    
    // Create floating 3D food symbol in AR
    _createFloatingFoodSymbol(symbol);

    // Mark matching symbols on card
    _gameState.card.markSymbol(symbol);
    _gameState.currentCallIndex++;

    // Check for win
    if (_gameState.card.checkForWin()) {
      _gameState.hasWon = true;
      _callerService.celebrateWin();
      _showWinDialog();
    }
  }

  void _createFloatingFoodSymbol(FoodSymbol symbol) async {
    try {
      // Create a floating food symbol above Chef Bob
      final foodNode = ARNode(
        type: NodeType.localGLTF2,
        uri: "assets/models/food_${symbol.id}.gltf", // We'll create these later
        scale: vector.Vector3(0.05, 0.05, 0.05),
        position: vector.Vector3(
          Random().nextDouble() * 0.4 - 0.2, // Random X position
          0.3, // Float above the table
          Random().nextDouble() * 0.4 - 0.2, // Random Z position
        ),
        rotation: vector.Vector4(0, 1, 0, Random().nextDouble() * 6.28), // Random rotation
      );

      final success = await arObjectManager!.addNode(foodNode);
      if (success) {
        _foodSymbolNodes.add(foodNode.name!);
        
        // Animate the food symbol (rotate and bob up and down)
        _animateFoodSymbol(foodNode.name!);
      }
    } catch (e) {
      print('Error creating floating food symbol: $e');
    }
  }

  void _animateFoodSymbol(String nodeName) {
    // Simple animation - in a real implementation, you'd use proper AR animations
    Timer.periodic(const Duration(milliseconds: 100), (timer) {
      if (_gameState.status != GameStatus.playing) {
        timer.cancel();
        return;
      }
      
      // This would update the node's position/rotation for animation
      // For now, we'll just let it float statically
    });
  }

  void _showWinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 BINGO!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Congratulations! You won in AR mode!'),
            const SizedBox(height: 16),
            Text('Points Earned: ${_gameState.pointsEarned}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Return to main menu
            },
            child: const Text('Play Again'),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    arSessionManager?.dispose();
    _callerService.dispose();
    _audioService.dispose();
    super.dispose();
  }
}
