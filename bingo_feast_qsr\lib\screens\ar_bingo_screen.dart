// 🥽 AR Bingo Screen
// Augmented Reality-style mode with camera and 3D overlays

import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:camera/camera.dart';

import '../models/bingo_card.dart';
import '../models/game_state.dart';
import '../models/food_symbol.dart';
import '../services/virtual_caller_service.dart';
import '../services/audio_service.dart';
import '../widgets/food_image_widget.dart';

// Simple floating food symbol for AR effect
class FloatingFoodSymbol {
  final FoodSymbol symbol;
  final Offset position;
  final double scale;
  final double rotation;
  final DateTime createdAt;

  FloatingFoodSymbol({
    required this.symbol,
    required this.position,
    this.scale = 1.0,
    this.rotation = 0.0,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();
}

// AR Grid painter for web fallback
class ARGridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withValues(alpha: 0.2)
      ..strokeWidth = 1.0
      ..style = PaintingStyle.stroke;

    // Draw grid lines
    const spacing = 50.0;

    // Vertical lines
    for (double x = 0; x < size.width; x += spacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // Horizontal lines
    for (double y = 0; y < size.height; y += spacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class ARBingoScreen extends StatefulWidget {
  final BingoCard bingoCard;

  const ARBingoScreen({
    super.key,
    required this.bingoCard,
  });

  @override
  State<ARBingoScreen> createState() => _ARBingoScreenState();
}

class _ARBingoScreenState extends State<ARBingoScreen>
    with TickerProviderStateMixin {
  // Camera
  CameraController? _cameraController;
  List<CameraDescription>? _cameras;

  // Game State
  late GameState _gameState;
  late VirtualCallerService _callerService;
  late AudioService _audioService;

  // AR-style State
  bool _isCameraInitialized = false;
  bool _isChefBobPlaced = false;
  bool _isGameStarted = false;

  // Animation Controllers for 3D effects
  late AnimationController _chefBobAnimationController;
  late AnimationController _foodFloatController;
  late AnimationController _particleController;
  late AnimationController _soundWaveController;
  late Animation<double> _chefBobBounce;
  late Animation<double> _foodFloat;
  late Animation<double> _particleAnimation;
  late Animation<double> _soundWaveAnimation;

  // Floating Food Symbols
  final List<FloatingFoodSymbol> _floatingFoods = [];

  // UI State
  FoodSymbol? _lastCalledSymbol;
  String _arInstructions = "Tap to place Chef Bob in your space!";
  final bool _chefBobTapped = false;
  final String _gameInstructions = "Tap Chef Bob to call the next food!";
  int _chefBobTapCount = 0; // Debug counter

  @override
  void initState() {
    super.initState();
    _initializeGame();
    _initializeCamera();
    _setupAnimations();
  }

  void _initializeGame() {
    _gameState = GameState(
      gameId: 'ar_game_${DateTime.now().millisecondsSinceEpoch}',
      card: widget.bingoCard,
      mode: GameMode.ar,
    );
    _callerService = VirtualCallerService();
    _audioService = AudioService();
  }

  void _initializeCamera() async {
    try {
      // For web, skip camera initialization and use fallback
      if (kIsWeb) {
        setState(() {
          _isCameraInitialized = true;
        });
        return;
      }

      _cameras = await availableCameras();
      if (_cameras!.isNotEmpty) {
        _cameraController = CameraController(
          _cameras![0],
          ResolutionPreset.medium,
        );
        await _cameraController!.initialize();
        if (mounted) {
          setState(() {
            _isCameraInitialized = true;
          });
        }
      }
    } catch (e) {
      debugPrint('Error initializing camera: $e');
      // Fallback: Set camera as initialized anyway
      setState(() {
        _isCameraInitialized = true;
      });
    }
  }

  void _setupAnimations() {
    _chefBobAnimationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _foodFloatController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    );

    _soundWaveController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _chefBobBounce = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _chefBobAnimationController,
      curve: Curves.easeInOut,
    ));

    _foodFloat = Tween<double>(
      begin: 0.0,
      end: 20.0,
    ).animate(CurvedAnimation(
      parent: _foodFloatController,
      curve: Curves.easeInOut,
    ));

    _particleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _particleController,
      curve: Curves.easeOut,
    ));

    _soundWaveAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _soundWaveController,
      curve: Curves.easeOut,
    ));

    _chefBobAnimationController.repeat(reverse: true);
    _foodFloatController.repeat(reverse: true);
    _particleController.repeat();
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              const Color(0xFF1A1A2E),
              const Color(0xFF16213E),
              const Color(0xFF0F3460),
              const Color(0xFF533483),
            ],
          ),
        ),
        child: SafeArea(
          child: Stack(
            children: [
              // Main content
              Column(
                children: [
                  // Clean AR Camera View
                  _buildCleanARCameraView(),

                  // Game content below camera
                  Expanded(
                    child: _buildGameContent(),
                  ),
                ],
              ),

              // Floating UI elements
              _buildFloatingUI(),
            ],
          ),
        ),
      ),
    );
  }

  // Clean, Beautiful AR Camera View
  Widget _buildCleanARCameraView() {
    final screenSize = MediaQuery.of(context).size;
    final cameraHeight = screenSize.height * 0.45;

    return Container(
      height: cameraHeight,
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: const Color(0xFF533483).withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(24),
        child: Stack(
          children: [
            // Beautiful AR Background
            Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFF2D1B69),
                    Color(0xFF11998E),
                    Color(0xFF38EF7D),
                  ],
                ),
              ),
              child: Stack(
                children: [
                  // Subtle AR Grid
                  CustomPaint(
                    size: Size.infinite,
                    painter: ARGridPainter(),
                  ),

                  // AR Mode Indicator
                  Positioned(
                    top: 16,
                    left: 16,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.6),
                        borderRadius: BorderRadius.circular(16),
                        border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.view_in_ar, color: Colors.white, size: 16),
                          const SizedBox(width: 6),
                          const Text(
                            'AR Mode',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Chef Bob in AR Space (when placed)
            if (_isChefBobPlaced) _buildARChefBob(),

            // Floating food symbols
            ..._floatingFoods.map((food) => _buildFloatingFood(food)),

            // AR Instructions (when not started)
            if (!_isGameStarted) _buildARInstructions(),
          ],
        ),
      ),
    );
  }

  // Clean Game Content Below Camera
  Widget _buildGameContent() {
    if (!_isGameStarted) {
      return _buildWelcomeContent();
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Chef Bob Caller Section
          _buildChefBobCaller(),

          const SizedBox(height: 20),

          // Bingo Card
          _buildBingoCard(),

          const SizedBox(height: 20),

          // Game Status
          _buildGameStatus(),
        ],
      ),
    );
  }

  // Floating UI Elements
  Widget _buildFloatingUI() {
    return Stack(
      children: [
        // Back Button
        Positioned(
          top: 16,
          left: 16,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.6),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
            ),
            child: IconButton(
              icon: const Icon(Icons.arrow_back, color: Colors.white),
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ),

        // Debug Panel (top right)
        if (_isGameStarted)
          Positioned(
            top: 16,
            right: 16,
            child: _buildDebugPanel(),
          ),
      ],
    );
  }

  // Floating back button with enhanced styling
  Widget _buildFloatingBackButton() {
    return Positioned(
      top: 20,
      left: 20,
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 600),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.scale(
            scale: 0.8 + (0.2 * value),
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.black.withValues(alpha: 0.8),
                    Colors.black.withValues(alpha: 0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(
                  color: Colors.white.withValues(alpha: 0.4),
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.cyan.withValues(alpha: 0.2),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Material(
                color: Colors.transparent,
                child: InkWell(
                  onTap: () => Navigator.of(context).pop(),
                  borderRadius: BorderRadius.circular(30),
                  child: Padding(
                    padding: const EdgeInsets.all(12),
                    child: Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                      shadows: [
                        Shadow(
                          color: Colors.cyan.withValues(alpha: 0.5),
                          blurRadius: 8,
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Enhanced debug panel
  Widget _buildEnhancedDebugPanel() {
    return Positioned(
      top: 80,
      right: 20,
      child: Column(
        children: [
          // Test button with enhanced styling
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.red.shade600, Colors.red.shade800],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.red.withValues(alpha: 0.4),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  final testSymbol = FoodSymbols.allSymbols.first;
                  debugPrint('Manual test: Creating floating food for ${testSymbol.name}');
                  _createFloatingFoodSymbol(testSymbol);
                },
                borderRadius: BorderRadius.circular(20),
                child: const Padding(
                  padding: EdgeInsets.all(8),
                  child: Icon(Icons.bug_report, color: Colors.white, size: 16),
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          // Enhanced debug info display
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.black.withValues(alpha: 0.8),
                  Colors.black.withValues(alpha: 0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.cyan.shade300.withValues(alpha: 0.3)),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Started: $_isGameStarted',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Chef: $_isChefBobPlaced',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Status: ${_gameState.status}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Taps: $_chefBobTapCount',
                  style: const TextStyle(color: Colors.yellow, fontSize: 10),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Seamless game content with better integration
  Widget _buildSeamlessGameContent() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isTablet ? 24 : 12,
        vertical: isTablet ? 20 : 16,
      ),
      child: Column(
        children: [
          // Enhanced immersive bingo card
          _buildEnhancedImmersiveBingoCard(),

          SizedBox(height: isTablet ? 24 : 16),

          // Enhanced Chef Bob caller section (moved from AR camera for better UX)
          _buildEnhancedChefBobCallerSection(),
        ],
      ),
    );
  }

  // Enhanced Chef Bob Caller Section - Fixed functionality and better UX
  Widget _buildEnhancedChefBobCallerSection() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: EdgeInsets.all(isTablet ? 24 : 20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.orange.shade100,
                    Colors.orange.shade200,
                    Colors.orange.shade300,
                  ],
                ),
                borderRadius: BorderRadius.circular(isTablet ? 28 : 24),
                border: Border.all(
                  color: Colors.orange.shade400,
                  width: 2,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                    spreadRadius: 5,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Enhanced title with animation
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.orange.shade600, Colors.orange.shade800],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withValues(alpha: 0.4),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Text(
                      '👨‍🍳 Chef Bob\'s Kitchen',
                      style: TextStyle(
                        fontSize: isTablet ? 26 : 22,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                        shadows: [
                          Shadow(
                            color: Colors.black.withValues(alpha: 0.3),
                            blurRadius: 4,
                            offset: const Offset(2, 2),
                          ),
                        ],
                      ),
                    ),
                  ),
                  SizedBox(height: isTablet ? 24 : 20),

                  // Enhanced Chef Bob Button - FIXED FUNCTIONALITY
                  _buildEnhancedChefBobButton(isTablet),

                  SizedBox(height: isTablet ? 20 : 16),

                  // Game status with beautiful styling
                  _buildEnhancedGameStatus(isTablet),

                  // Last called food display
                  if (_lastCalledSymbol != null) ...[
                    SizedBox(height: isTablet ? 20 : 16),
                    _buildEnhancedLastCalledFood(isTablet),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Enhanced Chef Bob Button with FIXED functionality
  Widget _buildEnhancedChefBobButton(bool isTablet) {
    return GestureDetector(
      onTap: () {
        debugPrint('🎯 Enhanced Chef Bob tapped!');
        debugPrint('🎮 Game started: $_isGameStarted');
        debugPrint('📊 Current call index: ${_gameState.currentCallIndex}');
        debugPrint('📋 Calling sequence length: ${_gameState.callingSequence.length}');

        setState(() {
          _chefBobTapCount++;
        });

        // Ensure game is started first
        if (!_isGameStarted) {
          debugPrint('🚀 Starting AR game...');
          _startARGame();
          return;
        }

        // Call the next symbol - FIXED FUNCTIONALITY
        debugPrint('📞 Calling next symbol...');
        _callNextSymbol();
      },
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        curve: Curves.elasticOut,
        width: isTablet ? 180 : 160,
        height: isTablet ? 180 : 160,
        decoration: BoxDecoration(
          gradient: RadialGradient(
            colors: [
              Colors.orange.shade200,
              Colors.orange.shade400,
              Colors.orange.shade700,
            ],
          ),
          borderRadius: BorderRadius.circular(isTablet ? 90 : 80),
          border: Border.all(
            color: Colors.orange.shade800,
            width: 4,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.5),
              blurRadius: 25,
              offset: const Offset(0, 12),
              spreadRadius: 3,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Enhanced Chef Hat
            Container(
              width: isTablet ? 60 : 50,
              height: isTablet ? 45 : 35,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(25),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.restaurant_menu,
                color: Colors.orange.shade600,
                size: isTablet ? 30 : 24,
              ),
            ),
            SizedBox(height: isTablet ? 16 : 12),

            // Enhanced Chef Face
            Icon(
              Icons.person,
              color: Colors.white,
              size: isTablet ? 50 : 40,
              shadows: [
                Shadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 4,
                  offset: const Offset(2, 2),
                ),
              ],
            ),
            SizedBox(height: isTablet ? 12 : 8),

            // Enhanced Call to Action
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: isTablet ? 16 : 12,
                vertical: isTablet ? 8 : 6,
              ),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.4),
                borderRadius: BorderRadius.circular(15),
              ),
              child: Text(
                _isGameStarted ? 'TAP TO\nCALL FOOD' : 'TAP TO\nSTART',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                  fontSize: isTablet ? 18 : 16,
                  shadows: [
                    Shadow(
                      color: Colors.orange.withValues(alpha: 0.8),
                      blurRadius: 6,
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Enhanced Game Status Display
  Widget _buildEnhancedGameStatus(bool isTablet) {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: isTablet ? 24 : 20,
        vertical: isTablet ? 16 : 12,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.white.withValues(alpha: 0.9),
            Colors.white.withValues(alpha: 0.7),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(color: Colors.orange.shade300, width: 2),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          // Calls Progress
          Column(
            children: [
              Text(
                'Calls Made',
                style: TextStyle(
                  fontSize: isTablet ? 14 : 12,
                  color: Colors.orange.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                '${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                style: TextStyle(
                  fontSize: isTablet ? 20 : 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ),

          // Vertical Divider
          Container(
            width: 2,
            height: 40,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.orange.shade300, Colors.orange.shade500],
              ),
            ),
          ),

          // Game Status
          Column(
            children: [
              Text(
                'Status',
                style: TextStyle(
                  fontSize: isTablet ? 14 : 12,
                  color: Colors.orange.shade600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                _gameState.status.name.toUpperCase(),
                style: TextStyle(
                  fontSize: isTablet ? 20 : 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.orange.shade700,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }







  // Enhanced Last Called Food Display
  Widget _buildEnhancedLastCalledFood(bool isTablet) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * value),
          child: Container(
            padding: EdgeInsets.all(isTablet ? 20 : 16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.white,
                  Colors.orange.shade50,
                  Colors.orange.shade100,
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.orange.shade300, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Enhanced food image with glow effect
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [
                        Colors.orange.shade200,
                        Colors.orange.shade400,
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.4),
                        blurRadius: 10,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: FoodImageWidget(
                    symbol: _lastCalledSymbol!,
                    size: isTablet ? 50 : 40,
                    isMarked: false,
                    isRevealed: true,
                  ),
                ),
                SizedBox(width: isTablet ? 20 : 16),

                // Enhanced text display
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Chef Bob Called:',
                        style: TextStyle(
                          fontSize: isTablet ? 16 : 14,
                          color: Colors.orange.shade600,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      SizedBox(height: isTablet ? 8 : 4),
                      Text(
                        _lastCalledSymbol!.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                          fontSize: isTablet ? 22 : 18,
                          shadows: [
                            Shadow(
                              color: Colors.orange.withValues(alpha: 0.3),
                              blurRadius: 4,
                              offset: const Offset(1, 1),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Enhanced Immersive Bingo Card
  Widget _buildEnhancedImmersiveBingoCard() {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final cardPadding = isTablet ? 24.0 : 16.0;
    final gridSpacing = isTablet ? 8.0 : 6.0;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1200),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 100 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              constraints: BoxConstraints(
                maxWidth: isTablet ? 600 : screenSize.width - 24,
              ),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.white,
                    Colors.blue.shade50,
                    Colors.purple.shade50,
                  ],
                ),
                borderRadius: BorderRadius.circular(isTablet ? 32 : 24),
                border: Border.all(
                  color: Colors.blue.shade300,
                  width: 3,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 25,
                    offset: const Offset(0, 15),
                    spreadRadius: 5,
                  ),
                  BoxShadow(
                    color: Colors.blue.withValues(alpha: 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Padding(
                padding: EdgeInsets.all(cardPadding),
                child: Column(
                  children: [
                    // Enhanced card header
                    Container(
                      padding: EdgeInsets.symmetric(
                        vertical: isTablet ? 16 : 12,
                        horizontal: isTablet ? 24 : 16,
                      ),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade400, Colors.purple.shade400],
                        ),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.grid_view,
                            color: Colors.white,
                            size: isTablet ? 24 : 20,
                          ),
                          SizedBox(width: isTablet ? 12 : 8),
                          Text(
                            '🥽 AR ${_gameState.card.gridSize}x${_gameState.card.gridSize} Bingo Card',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              fontSize: isTablet ? 18 : 16,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  blurRadius: 4,
                                  offset: const Offset(1, 1),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: isTablet ? 20 : 16),

                    // Enhanced bingo grid
                    AspectRatio(
                      aspectRatio: 1.0,
                      child: GridView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: _gameState.card.gridSize,
                          crossAxisSpacing: gridSpacing,
                          mainAxisSpacing: gridSpacing,
                        ),
                        itemCount: _gameState.card.totalSquares,
                        itemBuilder: (context, index) {
                          final row = index ~/ _gameState.card.gridSize;
                          final col = index % _gameState.card.gridSize;
                          final square = _gameState.card.grid[row][col];
                          return _buildEnhancedARBingoSquare(square, row, col, index, isTablet);
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDebugTestButton() {
    return Positioned(
      top: 60,
      right: 20,
      child: Column(
        children: [
          FloatingActionButton(
            mini: true,
            onPressed: () {
              // Test floating food creation
              final testSymbol = FoodSymbols.allSymbols.first;
              debugPrint('Manual test: Creating floating food for ${testSymbol.name}');
              _createFloatingFoodSymbol(testSymbol);
            },
            backgroundColor: Colors.red.withValues(alpha: 0.8),
            child: const Icon(Icons.bug_report, color: Colors.white, size: 16),
          ),
          const SizedBox(height: 8),
          // Debug info display
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.black.withValues(alpha: 0.7),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Started: $_isGameStarted',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Chef: $_isChefBobPlaced',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Status: ${_gameState.status}',
                  style: const TextStyle(color: Colors.white, fontSize: 10),
                ),
                Text(
                  'Taps: $_chefBobTapCount',
                  style: const TextStyle(color: Colors.yellow, fontSize: 10),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          // Test button for calling
          ElevatedButton(
            onPressed: () {
              debugPrint('🧪 TEST BUTTON: Calling _callNextSymbol directly');
              setState(() {
                _chefBobTapCount++;
              });
              if (!_isGameStarted) {
                _startARGame();
              } else {
                _callNextSymbol();
              }
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.green,
              minimumSize: const Size(60, 30),
            ),
            child: const Text(
              'TEST\nCALL',
              style: TextStyle(fontSize: 8, color: Colors.white),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBeautifulSoundWaves() {
    return AnimatedBuilder(
      animation: _soundWaveAnimation,
      builder: (context, child) {
        return Positioned(
          bottom: 140,
          left: MediaQuery.of(context).size.width * 0.5 - 80,
          child: SizedBox(
            width: 160,
            height: 160,
            child: Stack(
              children: List.generate(4, (index) {
                final scale = 0.2 + (index * 0.2) + (_soundWaveAnimation.value * 1.0);
                final opacity = (1.0 - _soundWaveAnimation.value) * (1.0 - index * 0.15);

                return Center(
                  child: Transform.scale(
                    scale: scale,
                    child: Opacity(
                      opacity: opacity,
                      child: Container(
                        width: 80,
                        height: 80,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: Colors.cyan.shade300,
                            width: 3,
                          ),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.cyan.withValues(alpha: 0.4),
                              blurRadius: 10,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedFloatingFood(FloatingFoodSymbol food) {
    final age = DateTime.now().difference(food.createdAt).inSeconds;
    final opacity = (1.0 - (age / 10.0)).clamp(0.0, 1.0);
    final pulseScale = 1.0 + (sin(_foodFloat.value * 2 * pi) * 0.15);

    return AnimatedBuilder(
      animation: _foodFloat,
      builder: (context, child) {
        return Positioned(
          left: food.position.dx,
          top: food.position.dy - _foodFloat.value,
          child: Transform.rotate(
            angle: food.rotation + (_foodFloat.value * 0.1),
            child: Transform.scale(
              scale: food.scale * pulseScale,
              child: Opacity(
                opacity: opacity,
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [
                        Colors.white,
                        Colors.cyan.shade50,
                        Colors.blue.shade100,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: Colors.cyan.shade300,
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.cyan.withValues(alpha: 0.4),
                        blurRadius: 15,
                        offset: const Offset(0, 6),
                        spreadRadius: 3,
                      ),
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, 3),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FoodImageWidget(
                        symbol: food.symbol,
                        size: 50,
                        isMarked: false,
                        isRevealed: true,
                      ),
                      const SizedBox(height: 6),
                      Text(
                        food.symbol.name,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                          shadows: [
                            Shadow(
                              color: Colors.white.withValues(alpha: 0.8),
                              blurRadius: 2,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildBeautifulARInstructions() {
    return Positioned(
      bottom: 60,
      left: 20,
      right: 20,
      child: GestureDetector(
        onTap: _isCameraInitialized && !_isChefBobPlaced ? _placeChefBob : null,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Colors.black.withValues(alpha: 0.9),
                Colors.black.withValues(alpha: 0.7),
              ],
            ),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: Colors.cyan.shade300.withValues(alpha: 0.6),
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.3),
                blurRadius: 15,
                offset: const Offset(0, 8),
              ),
            ],
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.cyan.shade300.withValues(alpha: 0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  _isCameraInitialized ? Icons.touch_app : Icons.camera_alt,
                  color: Colors.cyan.shade300,
                  size: 40,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                _arInstructions,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              if (_isCameraInitialized && !_isChefBobPlaced) ...[
                const SizedBox(height: 20),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [Colors.orange.shade400, Colors.orange.shade600],
                    ),
                    borderRadius: BorderRadius.circular(30),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.orange.withValues(alpha: 0.4),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Text(
                    '✨ Tap to Place Chef Bob ✨',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _placeChefBob() {
    if (_isChefBobPlaced) return;

    setState(() {
      _isChefBobPlaced = true;
      _arInstructions = "Perfect! Chef Bob is ready to call bingo!";
    });

    // Start the game immediately for testing
    _startARGame();
  }

  Widget _buildChefBobCallerSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.shade100, Colors.orange.shade200],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '👨‍🍳 Chef Bob\'s Kitchen',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.orange,
            ),
          ),
          const SizedBox(height: 12),

          // Large Chef Bob Button
          GestureDetector(
            onTap: () {
              debugPrint('🎯 Large Chef Bob tapped!');
              setState(() {
                _chefBobTapCount++;
              });
              if (!_isGameStarted) {
                _startARGame();
              } else {
                _callNextSymbol();
              }
            },
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.orange.shade300, Colors.orange.shade600],
                ),
                borderRadius: BorderRadius.circular(60),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.3),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: const Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.restaurant_menu, color: Colors.white, size: 40),
                  SizedBox(height: 8),
                  Text(
                    'TAP TO\nCALL FOOD',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 12),

          // Game Status
          Text(
            'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Colors.orange,
            ),
          ),

          // Last Called Food
          if (_lastCalledSymbol != null) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade300),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FoodImageWidget(
                    symbol: _lastCalledSymbol!,
                    size: 30,
                    isMarked: false,
                    isRevealed: true,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Last Called: ${_lastCalledSymbol!.name}',
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.orange,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildImmersiveBingoCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.blue.shade50,
            Colors.blue.shade100,
            Colors.cyan.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.blue.shade300,
          width: 3,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.blue.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        children: [
          // Beautiful title
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.blue.shade600, Colors.blue.shade800],
              ),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.blue.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Text(
              '🎯 Your Bingo Card',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Full 5x5 Bingo Grid with beautiful styling
          AspectRatio(
            aspectRatio: 1.0,
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.8),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.blue.shade200, width: 2),
              ),
              child: GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: _gameState.card.gridSize,
                  crossAxisSpacing: 6,
                  mainAxisSpacing: 6,
                ),
                itemCount: _gameState.card.gridSize * _gameState.card.gridSize,
                itemBuilder: (context, index) {
                  final row = index ~/ _gameState.card.gridSize;
                  final col = index % _gameState.card.gridSize;
                  final square = _gameState.card.grid[row][col];

                  return AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    curve: Curves.elasticOut,
                    decoration: BoxDecoration(
                      gradient: square.isMarked
                          ? LinearGradient(
                              colors: [
                                Colors.green.shade300,
                                Colors.green.shade600,
                              ],
                            )
                          : LinearGradient(
                              colors: [
                                Colors.white,
                                Colors.grey.shade50,
                              ],
                            ),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: square.isMarked
                            ? Colors.green.shade700
                            : Colors.blue.shade300,
                        width: square.isMarked ? 3 : 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: square.isMarked
                              ? Colors.green.withValues(alpha: 0.4)
                              : Colors.blue.withValues(alpha: 0.1),
                          blurRadius: square.isMarked ? 8 : 4,
                          offset: const Offset(0, 3),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        FoodImageWidget(
                          symbol: square.symbol,
                          size: 36,
                          isMarked: square.isMarked,
                          isRevealed: true, // Always show in AR mode
                        ),
                        const SizedBox(height: 4),
                        Text(
                          square.symbol.name,
                          style: TextStyle(
                            fontSize: 9,
                            fontWeight: FontWeight.bold,
                            color: square.isMarked ? Colors.white : Colors.black87,
                            shadows: square.isMarked ? [
                              const Shadow(
                                color: Colors.black26,
                                blurRadius: 2,
                                offset: Offset(1, 1),
                              ),
                            ] : null,
                          ),
                          textAlign: TextAlign.center,
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Game instructions
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Colors.orange.shade100.withValues(alpha: 0.8),
                  Colors.orange.shade200.withValues(alpha: 0.6),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.orange.shade300, width: 2),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.orange.shade600,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.restaurant_menu,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    const Text(
                      'How to Play',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.orange,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Text(
                  '👆 Tap the orange Chef Bob button in the AR camera above to call foods!\n\n🎯 Squares will automatically mark when foods are called.\n\n🏆 Get 5 in a row to win BINGO!',
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: Colors.orange.shade800,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBeautifulGameOverlay() {
    return Positioned(
      top: 60,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.white.withValues(alpha: 0.95),
              Colors.white.withValues(alpha: 0.85),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Colors.cyan.shade300.withValues(alpha: 0.6),
            width: 2,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.2),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          children: [
            // Progress Bar with beautiful styling
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.shade100,
                    shape: BoxShape.circle,
                  ),
                  child: Icon(Icons.timer, color: Colors.blue.shade600, size: 20),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progress: ${(_gameState.progress * 100).toInt()}%',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.blue.shade800,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Container(
                        height: 8,
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(4),
                          color: Colors.grey.shade300,
                        ),
                        child: FractionallySizedBox(
                          alignment: Alignment.centerLeft,
                          widthFactor: _gameState.progress,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              gradient: LinearGradient(
                                colors: [Colors.blue.shade400, Colors.blue.shade600],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Last Called Symbol with beautiful styling
            if (_lastCalledSymbol != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade100, Colors.orange.shade200],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(color: Colors.orange.shade300),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    FoodImageWidget(
                      symbol: _lastCalledSymbol!,
                      size: 40,
                      isMarked: false,
                      isRevealed: true,
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Just Called:',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        Text(
                          _lastCalledSymbol!.name,
                          style: const TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildBeautifulChefBobCaller() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange.shade50,
            Colors.orange.shade100,
            Colors.orange.shade200,
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: Colors.orange.shade300,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          // Beautiful title
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.orange.shade600,
              borderRadius: BorderRadius.circular(20),
            ),
            child: const Text(
              '👨‍🍳 Chef Bob\'s Kitchen',
              style: TextStyle(
                fontSize: 22,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
          ),
          const SizedBox(height: 20),

          // Large Beautiful Chef Bob Button with animations
          GestureDetector(
            onTap: () {
              debugPrint('🎯 Beautiful Large Chef Bob tapped!');
              setState(() {
                _chefBobTapCount++;
              });

              // Ensure game is started
              if (!_isGameStarted) {
                _startARGame();
              } else {
                _callNextSymbol();
              }
            },
            child: AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              curve: Curves.elasticOut,
              width: 160,
              height: 160,
              decoration: BoxDecoration(
                gradient: RadialGradient(
                  colors: [
                    Colors.orange.shade200,
                    Colors.orange.shade400,
                    Colors.orange.shade700,
                  ],
                ),
                borderRadius: BorderRadius.circular(80),
                border: Border.all(
                  color: Colors.orange.shade800,
                  width: 4,
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.4),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.2),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.restaurant_menu,
                    color: Colors.white,
                    size: 50,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        blurRadius: 4,
                        offset: const Offset(2, 2),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    'TAP TO\nCALL FOOD',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      shadows: [
                        Shadow(
                          color: Colors.black26,
                          blurRadius: 2,
                          offset: Offset(1, 1),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 20),

          // Game Status with beautiful styling
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.8),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(color: Colors.orange.shade300),
            ),
            child: Text(
              'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.orange.shade700,
              ),
            ),
          ),

          // Last Called Food with beautiful styling
          if (_lastCalledSymbol != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(color: Colors.orange.shade300, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: Colors.orange.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  FoodImageWidget(
                    symbol: _lastCalledSymbol!,
                    size: 40,
                    isMarked: false,
                    isRevealed: true,
                  ),
                  const SizedBox(width: 12),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        'Last Called:',
                        style: TextStyle(
                          fontSize: 12,
                          color: Colors.orange,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        _lastCalledSymbol!.name,
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  void _startARGame() async {
    setState(() {
      _isGameStarted = true;
    });

    // Reset game state
    _gameState.currentCallIndex = 0;
    _gameState.status = GameStatus.playing;
    _gameState.hasWon = false;

    // Generate calling sequence first
    _gameState.generateCallingSequence();
    _gameState.startGame();

    // Reveal all squares in AR mode (not a scratch game)
    for (int row = 0; row < _gameState.card.gridSize; row++) {
      for (int col = 0; col < _gameState.card.gridSize; col++) {
        _gameState.card.grid[row][col].isRevealed = true;
      }
    }

    // Debug: Check if calling sequence is generated
    debugPrint('AR Game Started!');
    debugPrint('Calling sequence length: ${_gameState.callingSequence.length}');
    debugPrint('Game status: ${_gameState.status}');
    debugPrint('All squares revealed for AR mode');

    await _callerService.greetPlayer();

    // Manual calling mode - user taps Chef Bob to call foods
    // No automatic calling
  }



  void _callNextSymbol() {
    debugPrint('🎯 _callNextSymbol called');
    debugPrint('📊 Current call index: ${_gameState.currentCallIndex}');
    debugPrint('📋 Calling sequence length: ${_gameState.callingSequence.length}');
    debugPrint('🎮 Game status: ${_gameState.status}');

    // Check if calling sequence is empty
    if (_gameState.callingSequence.isEmpty) {
      debugPrint('❌ ERROR: Calling sequence is empty! Generating now...');
      _gameState.generateCallingSequence();
      debugPrint('✅ Generated calling sequence with ${_gameState.callingSequence.length} symbols');
    }

    if (_gameState.currentCallIndex >= _gameState.callingSequence.length) {
      debugPrint('🏁 Game completed - no more symbols to call');
      return;
    }

    // Don't complete game until max calls reached
    if (_gameState.currentCallIndex >= _gameState.maxCalls) {
      debugPrint('🏁 Reached max calls - game completed');
      _endARGameWithoutWin();
      return;
    }

    final symbol = _gameState.callingSequence[_gameState.currentCallIndex];
    debugPrint('📢 Calling symbol: ${symbol.name}');
    debugPrint('🎯 Symbol index in sequence: ${_gameState.currentCallIndex}');

    // Update UI
    setState(() {
      _lastCalledSymbol = symbol;
    });

    // Make Chef Bob call it
    _callerService.callSymbol(symbol);

    // Trigger sound wave animation
    _soundWaveController.forward().then((_) {
      _soundWaveController.reset();
    });

    // Create floating 3D food symbol in AR
    _createFloatingFoodSymbol(symbol);
    debugPrint('Created floating food symbol for: ${symbol.name}');

    // Mark matching symbols on card
    final markedCount = _gameState.card.markSymbol(symbol);
    debugPrint('🎯 Marked $markedCount squares for ${symbol.name}');

    _gameState.currentCallIndex++;
    debugPrint('📊 Updated call index to: ${_gameState.currentCallIndex}');

    // Check for win
    if (_gameState.card.checkForWin()) {
      _gameState.hasWon = true;
      _callerService.celebrateWin();
      _showWinDialog();
    }
  }

  void _createFloatingFoodSymbol(FoodSymbol symbol) {
    final screenSize = MediaQuery.of(context).size;
    final random = Random();

    debugPrint('Creating floating food symbol for: ${symbol.name}');
    debugPrint('Screen size: ${screenSize.width} x ${screenSize.height}');

    // Create a floating food symbol at a random position
    final floatingFood = FloatingFoodSymbol(
      symbol: symbol,
      position: Offset(
        random.nextDouble() * (screenSize.width - 100) + 50,
        random.nextDouble() * 200 + 100, // Top area of screen
      ),
      scale: 0.8 + random.nextDouble() * 0.4, // Random scale 0.8-1.2
      rotation: random.nextDouble() * 6.28, // Random rotation
    );

    debugPrint('Floating food position: ${floatingFood.position}');
    debugPrint('Total floating foods before add: ${_floatingFoods.length}');

    setState(() {
      _floatingFoods.add(floatingFood);
    });

    debugPrint('Total floating foods after add: ${_floatingFoods.length}');

    // Remove the floating food after 10 seconds
    Timer(const Duration(seconds: 10), () {
      setState(() {
        _floatingFoods.remove(floatingFood);
      });
      debugPrint('Removed floating food: ${symbol.name}');
    });
  }

  // End AR game without win (max calls reached)
  void _endARGameWithoutWin() {
    debugPrint('🏁 AR Game ended without win - max calls reached');

    setState(() {
      _gameState.status = GameStatus.completed;
      _gameState.hasWon = false;
      _gameState.pointsEarned = 5; // Consolation points
    });

    // Show consolation dialog
    _showConsolationDialog();
  }

  void _showWinDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎉 BINGO!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Congratulations! You won in AR mode!'),
            const SizedBox(height: 16),
            Text('Points Earned: ${_gameState.pointsEarned}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Return to main menu
            },
            child: const Text('Play Again'),
          ),
        ],
      ),
    );
  }

  void _showConsolationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('🎮 Game Over'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Good try! You reached the maximum number of calls.'),
            const SizedBox(height: 16),
            Text('Consolation Points: ${_gameState.pointsEarned}'),
            const SizedBox(height: 8),
            const Text('Keep playing to improve your luck!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Return to main menu
            },
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  // Enhanced AR Bingo Square with beautiful animations
  Widget _buildEnhancedARBingoSquare(BingoSquare square, int row, int col, int index, bool isTablet) {
    final isMarked = square.isMarked;
    final isCenterFree = _gameState.card.type == BingoCardType.small5x5 && row == 2 && col == 2;
    final squareSize = isTablet ? 60.0 : 50.0;

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 400 + (index * 50)), // Staggered animation
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: _getEnhancedSquareColors(isMarked, isCenterFree),
                ),
                borderRadius: BorderRadius.circular(isTablet ? 16 : 12),
                border: Border.all(
                  color: isMarked
                      ? Colors.green.shade600
                      : Colors.blue.shade300,
                  width: isMarked ? 3 : 2,
                ),
                boxShadow: [
                  if (isMarked) BoxShadow(
                    color: Colors.green.withValues(alpha: 0.5),
                    blurRadius: 12,
                    offset: const Offset(0, 6),
                    spreadRadius: 2,
                  ),
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Main content
                  Padding(
                    padding: EdgeInsets.all(isTablet ? 8 : 6),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        if (isCenterFree) ...[
                          // Enhanced FREE space
                          Container(
                            width: squareSize * 0.6,
                            height: squareSize * 0.6,
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  Colors.orange.shade300,
                                  Colors.orange.shade600,
                                ],
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.4),
                                  blurRadius: 8,
                                  spreadRadius: 2,
                                ),
                              ],
                            ),
                            child: Center(
                              child: Text(
                                'FREE',
                                style: TextStyle(
                                  fontSize: isTablet ? 12 : 10,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ] else ...[
                          // Enhanced food symbol
                          FoodImageWidget(
                            symbol: square.symbol,
                            size: isTablet ? 36 : 28,
                            isMarked: isMarked,
                            isRevealed: true, // Always revealed in AR mode
                          ),
                          SizedBox(height: isTablet ? 6 : 4),
                          Text(
                            square.symbol.name,
                            style: TextStyle(
                              fontSize: isTablet ? 10 : 8,
                              fontWeight: FontWeight.bold,
                              color: isMarked ? Colors.white : Colors.grey.shade800,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),

                  // Enhanced check mark for marked squares
                  if (isMarked)
                    Positioned(
                      top: 4,
                      right: 4,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          gradient: RadialGradient(
                            colors: [
                              Colors.green.shade400,
                              Colors.green.shade700,
                            ],
                          ),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.green.withValues(alpha: 0.6),
                              blurRadius: 6,
                              spreadRadius: 1,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.check,
                          color: Colors.white,
                          size: isTablet ? 16 : 12,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  // Enhanced square colors for AR mode
  List<Color> _getEnhancedSquareColors(bool isMarked, bool isCenterFree) {
    if (isMarked) {
      return [Colors.green.shade300, Colors.green.shade600];
    } else if (isCenterFree) {
      return [Colors.orange.shade100, Colors.orange.shade300];
    } else {
      return [Colors.white, Colors.blue.shade50];
    }
  }

  // Floating AR particles for ambiance
  Widget _buildFloatingARParticles() {
    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        return Stack(
          children: List.generate(8, (index) {
            final angle = (index * 45.0) * (3.14159 / 180.0);
            final radius = 100.0 + (_particleAnimation.value * 80.0);
            final screenSize = MediaQuery.of(context).size;
            final centerX = screenSize.width * 0.5;
            final centerY = 200.0;
            final x = centerX + (radius * cos(angle));
            final y = centerY + (radius * sin(angle));

            return Positioned(
              left: x - 4,
              top: y - 4,
              child: Opacity(
                opacity: (1.0 - _particleAnimation.value) * 0.6,
                child: Container(
                  width: 8,
                  height: 8,
                  decoration: BoxDecoration(
                    gradient: RadialGradient(
                      colors: [
                        Colors.cyan.shade300,
                        Colors.blue.shade400,
                      ],
                    ),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.cyan.withValues(alpha: 0.4),
                        blurRadius: 6,
                        spreadRadius: 1,
                      ),
                    ],
                  ),
                ),
              ),
            );
          }),
        );
      },
    );
  }

  // Enhanced 3D Chef Bob for AR camera section
  Widget _buildEnhanced3DChefBob() {
    return AnimatedBuilder(
      animation: _chefBobAnimationController,
      builder: (context, child) {
        return Positioned(
          bottom: 100,
          left: MediaQuery.of(context).size.width * 0.5 - 80,
          child: Container(
            width: 160,
            height: 180,
            decoration: BoxDecoration(
              gradient: RadialGradient(
                colors: [
                  Colors.orange.shade200,
                  Colors.orange.shade500,
                  Colors.orange.shade800,
                ],
              ),
              borderRadius: BorderRadius.circular(30),
              border: Border.all(
                color: Colors.orange.shade300,
                width: 3,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.orange.withValues(alpha: 0.6),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                  spreadRadius: 3,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // 3D Chef Hat
                Container(
                  width: 60,
                  height: 40,
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        blurRadius: 8,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.restaurant_menu,
                    color: Colors.orange.shade600,
                    size: 28,
                  ),
                ),
                const SizedBox(height: 16),

                // 3D Chef Face
                Icon(
                  Icons.person,
                  color: Colors.white,
                  size: 50,
                  shadows: [
                    Shadow(
                      color: Colors.black.withValues(alpha: 0.4),
                      blurRadius: 6,
                      offset: const Offset(2, 2),
                    ),
                  ],
                ),
                const SizedBox(height: 12),

                // 3D Chef Name
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.4),
                    borderRadius: BorderRadius.circular(15),
                  ),
                  child: Text(
                    'Chef Bob 3D',
                    style: TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                      shadows: [
                        Shadow(
                          color: Colors.orange.withValues(alpha: 0.8),
                          blurRadius: 6,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // Enhanced magical particles
  Widget _buildEnhancedMagicalParticles() {
    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        return Positioned(
          bottom: 100,
          left: MediaQuery.of(context).size.width * 0.5 - 120,
          child: SizedBox(
            width: 240,
            height: 240,
            child: Stack(
              children: List.generate(16, (index) {
                final angle = (index * 22.5) * (3.14159 / 180.0);
                final radius = 80.0 + (_particleAnimation.value * 60.0);
                final x = 120 + (radius * cos(angle));
                final y = 120 + (radius * sin(angle));

                return Positioned(
                  left: x - 8,
                  top: y - 8,
                  child: Opacity(
                    opacity: (1.0 - _particleAnimation.value) * 0.8,
                    child: Container(
                      width: 16,
                      height: 16,
                      decoration: BoxDecoration(
                        gradient: RadialGradient(
                          colors: [
                            Colors.yellow.shade300,
                            Colors.orange.shade500,
                            Colors.red.shade600,
                          ],
                        ),
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.orange.withValues(alpha: 0.7),
                            blurRadius: 10,
                            spreadRadius: 3,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  // Enhanced sound waves
  Widget _buildEnhancedSoundWaves() {
    return AnimatedBuilder(
      animation: _soundWaveController,
      builder: (context, child) {
        return Positioned(
          bottom: 180,
          left: MediaQuery.of(context).size.width * 0.5 - 100,
          child: SizedBox(
            width: 200,
            height: 100,
            child: Stack(
              children: List.generate(5, (index) {
                final waveRadius = 20.0 + (index * 15.0) + (_soundWaveAnimation.value * 50.0);
                final opacity = (1.0 - _soundWaveAnimation.value) * (1.0 - index * 0.15);

                return Positioned(
                  left: 100 - waveRadius,
                  top: 50 - waveRadius,
                  child: Opacity(
                    opacity: opacity.clamp(0.0, 1.0),
                    child: Container(
                      width: waveRadius * 2,
                      height: waveRadius * 2,
                      decoration: BoxDecoration(
                        border: Border.all(
                          color: Colors.cyan.shade300,
                          width: 2,
                        ),
                        shape: BoxShape.circle,
                      ),
                    ),
                  ),
                );
              }),
            ),
          ),
        );
      },
    );
  }

  // Enhanced AR instructions
  Widget _buildEnhancedARInstructions() {
    return Positioned(
      bottom: 80,
      left: 20,
      right: 20,
      child: TweenAnimationBuilder<double>(
        duration: const Duration(milliseconds: 1000),
        tween: Tween(begin: 0.0, end: 1.0),
        builder: (context, value, child) {
          return Transform.translate(
            offset: Offset(0, 50 * (1 - value)),
            child: Opacity(
              opacity: value,
              child: Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.black.withValues(alpha: 0.8),
                      Colors.black.withValues(alpha: 0.6),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: Colors.cyan.shade300, width: 2),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.cyan.withValues(alpha: 0.3),
                      blurRadius: 15,
                      spreadRadius: 3,
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.cyan.shade300.withValues(alpha: 0.2),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        _isCameraInitialized ? Icons.touch_app : Icons.camera_alt,
                        color: Colors.cyan.shade300,
                        size: 40,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _arInstructions,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.cyan.shade400, Colors.blue.shade500],
                        ),
                        borderRadius: BorderRadius.circular(25),
                      ),
                      child: GestureDetector(
                        onTap: () {
                          setState(() {
                            _isChefBobPlaced = true;
                            _arInstructions = "Chef Bob is ready! Tap him to start the game!";
                          });
                        },
                        child: const Text(
                          'Place Chef Bob',
                          style: TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  // Enhanced game progress overlay
  Widget _buildEnhancedGameProgressOverlay() {
    return Positioned(
      top: 80,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Colors.black.withValues(alpha: 0.8),
              Colors.black.withValues(alpha: 0.6),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.cyan.shade300, width: 1),
          boxShadow: [
            BoxShadow(
              color: Colors.cyan.withValues(alpha: 0.2),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            // Progress indicator
            Column(
              children: [
                Text(
                  'Progress',
                  style: TextStyle(
                    color: Colors.cyan.shade300,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${(_gameState.progress * 100).toInt()}%',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),

            // Calls indicator
            Column(
              children: [
                Text(
                  'Calls',
                  style: TextStyle(
                    color: Colors.cyan.shade300,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Beautiful AR Chef Bob
  Widget _buildARChefBob() {
    return Positioned(
      bottom: 60,
      left: MediaQuery.of(context).size.width * 0.5 - 50,
      child: Container(
        width: 100,
        height: 120,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFFFF6B6B),
              Color(0xFFFF8E53),
              Color(0xFFFF6B35),
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 15,
              offset: const Offset(0, 8),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Chef Hat
            Container(
              width: 40,
              height: 25,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(15),
              ),
              child: const Icon(
                Icons.restaurant_menu,
                color: Color(0xFFFF6B35),
                size: 16,
              ),
            ),
            const SizedBox(height: 8),

            // Chef Face
            const Icon(
              Icons.person,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 4),

            // Chef Name
            const Text(
              'Chef Bob',
              style: TextStyle(
                color: Colors.white,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Simple floating food
  Widget _buildFloatingFood(FloatingFoodSymbol food) {
    return Positioned(
      left: food.position.dx,
      top: food.position.dy,
      child: Transform.scale(
        scale: food.scale,
        child: Transform.rotate(
          angle: food.rotation,
          child: Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Center(
              child: FoodImageWidget(
                symbol: food.symbol,
                size: 24,
                isMarked: false,
                isRevealed: true,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // Clean AR Instructions
  Widget _buildARInstructions() {
    return Positioned(
      bottom: 20,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.white.withValues(alpha: 0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(
              Icons.touch_app,
              color: Colors.white,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              _arInstructions,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isChefBobPlaced = true;
                  _arInstructions = "Chef Bob is ready! Start the game below.";
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF11998E),
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: const Text('Place Chef Bob'),
            ),
          ],
        ),
      ),
    );
  }

  // Welcome content when game hasn't started
  Widget _buildWelcomeContent() {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              borderRadius: BorderRadius.circular(24),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 20,
                  offset: const Offset(0, 10),
                ),
              ],
            ),
            child: Column(
              children: [
                const Icon(
                  Icons.view_in_ar,
                  size: 64,
                  color: Colors.white,
                ),
                const SizedBox(height: 16),
                const Text(
                  'Welcome to AR Bingo!',
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),
                const Text(
                  'Place Chef Bob in your AR space above, then start playing!',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white70,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                if (_isChefBobPlaced)
                  ElevatedButton(
                    onPressed: _startARGame,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: const Color(0xFF11998E),
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(25),
                      ),
                    ),
                    child: const Text(
                      'Start AR Bingo Game',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Clean Chef Bob Caller
  Widget _buildChefBobCaller() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFFFF6B6B), Color(0xFFFF8E53)],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          const Text(
            '👨‍🍳 Chef Bob\'s Kitchen',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          // MAIN CHEF BOB BUTTON - FIXED FUNCTIONALITY
          GestureDetector(
            onTap: () {
              debugPrint('🎯 Chef Bob Caller Tapped!');
              debugPrint('🎮 Game started: $_isGameStarted');
              debugPrint('📊 Current call index: ${_gameState.currentCallIndex}');

              if (!_isGameStarted) {
                debugPrint('🚀 Starting AR game...');
                _startARGame();
              } else {
                debugPrint('📞 Calling next symbol...');
                _callNextSymbol();
              }
            },
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Color(0xFFFFD93D), Color(0xFFFF6B35)],
                ),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.restaurant_menu,
                    size: 40,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _isGameStarted ? 'TAP TO\nCALL' : 'TAP TO\nSTART',
                    textAlign: TextAlign.center,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Game progress
          if (_isGameStarted)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                'Calls: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Clean Bingo Card
  Widget _buildBingoCard() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        children: [
          // Card Header
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '🥽 AR ${_gameState.card.gridSize}x${_gameState.card.gridSize} Bingo Card',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Bingo Grid
          AspectRatio(
            aspectRatio: 1.0,
            child: GridView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: _gameState.card.gridSize,
                crossAxisSpacing: 4,
                mainAxisSpacing: 4,
              ),
              itemCount: _gameState.card.totalSquares,
              itemBuilder: (context, index) {
                final row = index ~/ _gameState.card.gridSize;
                final col = index % _gameState.card.gridSize;
                final square = _gameState.card.grid[row][col];
                return _buildBingoSquare(square, row, col);
              },
            ),
          ),
        ],
      ),
    );
  }

  // Clean Bingo Square
  Widget _buildBingoSquare(BingoSquare square, int row, int col) {
    final isMarked = square.isMarked;
    final isCenterFree = _gameState.card.type == BingoCardType.small5x5 && row == 2 && col == 2;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isMarked
              ? [const Color(0xFF11998E), const Color(0xFF38EF7D)]
              : isCenterFree
                  ? [const Color(0xFFFFD93D), const Color(0xFFFF6B35)]
                  : [Colors.grey.shade100, Colors.grey.shade200],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isMarked ? const Color(0xFF11998E) : Colors.grey.shade300,
          width: isMarked ? 2 : 1,
        ),
      ),
      child: Stack(
        children: [
          // Main content
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (isCenterFree) ...[
                  const Text(
                    'FREE',
                    style: TextStyle(
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ] else ...[
                  FoodImageWidget(
                    symbol: square.symbol,
                    size: 20,
                    isMarked: isMarked,
                    isRevealed: true,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    square.symbol.name,
                    style: TextStyle(
                      fontSize: 6,
                      fontWeight: FontWeight.bold,
                      color: isMarked ? Colors.white : Colors.grey.shade800,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
              ],
            ),
          ),

          // Check mark for marked squares
          if (isMarked)
            Positioned(
              top: 2,
              right: 2,
              child: Container(
                width: 16,
                height: 16,
                decoration: const BoxDecoration(
                  color: Colors.white,
                  shape: BoxShape.circle,
                ),
                child: const Icon(
                  Icons.check,
                  size: 12,
                  color: Color(0xFF11998E),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // Clean Game Status
  Widget _buildGameStatus() {
    if (_lastCalledSymbol == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF667eea), Color(0xFF764ba2)],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(25),
            ),
            child: Center(
              child: FoodImageWidget(
                symbol: _lastCalledSymbol!,
                size: 30,
                isMarked: false,
                isRevealed: true,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Last Called:',
                  style: TextStyle(
                    color: Colors.white70,
                    fontSize: 12,
                  ),
                ),
                Text(
                  _lastCalledSymbol!.name,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Simple Debug Panel
  Widget _buildDebugPanel() {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: Colors.black.withValues(alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Debug: ${_gameState.currentCallIndex}/${_gameState.callingSequence.length}',
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
          Text(
            'Status: ${_gameState.status.name}',
            style: const TextStyle(color: Colors.white, fontSize: 10),
          ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _cameraController?.dispose();
    _chefBobAnimationController.dispose();
    _foodFloatController.dispose();
    _particleController.dispose();
    _soundWaveController.dispose();
    _callerService.dispose();
    _audioService.dispose();
    super.dispose();
  }
}
