@echo off
echo Starting Hatch 'n' Wing Bingo Feast Game Server...
cd build\web
start http://localhost:8080
echo Game should open in your browser at: http://localhost:8080
echo.
echo Starting simple HTTP server...
powershell -Command "& {$listener = New-Object System.Net.HttpListener; $listener.Prefixes.Add('http://localhost:8080/'); $listener.Start(); Write-Host 'Server running at http://localhost:8080 - Press Ctrl+C to stop'; try { while ($true) { $context = $listener.GetContext(); $request = $context.Request; $response = $context.Response; $localPath = $request.Url.LocalPath; if ($localPath -eq '/') { $localPath = '/index.html' }; $filePath = Join-Path (Get-Location) $localPath.TrimStart('/'); Write-Host \"Request: $localPath\"; if (Test-Path $filePath) { $content = [System.IO.File]::ReadAllBytes($filePath); $ext = [System.IO.Path]::GetExtension($filePath); switch ($ext) { '.html' { $response.ContentType = 'text/html' } '.js' { $response.ContentType = 'application/javascript' } '.css' { $response.ContentType = 'text/css' } '.json' { $response.ContentType = 'application/json' } '.wasm' { $response.ContentType = 'application/wasm' } default { $response.ContentType = 'application/octet-stream' } }; $response.ContentLength64 = $content.Length; $response.OutputStream.Write($content, 0, $content.Length); $response.StatusCode = 200 } else { $response.StatusCode = 404; Write-Host \"File not found: $filePath\" }; $response.Close() } } catch { Write-Host \"Server stopped\" } finally { $listener.Stop() }}"