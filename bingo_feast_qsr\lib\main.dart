// 🎮 Hatch 'n' Wing Bingo Feast - Main App
// This is the entry point for our bingo game

import 'package:flutter/material.dart';
import 'models/bingo_card.dart';
import 'models/game_state.dart';
import 'screens/bingo_game_screen.dart';
import 'services/daily_plays_service.dart';
import 'models/daily_plays.dart';

// Reusable hover animation widget
class HoverAnimatedContainer extends StatefulWidget {
  final Widget child;
  final double hoverScale;
  final Duration duration;
  final VoidCallback? onTap;

  const HoverAnimatedContainer({
    super.key,
    required this.child,
    this.hoverScale = 1.05,
    this.duration = const Duration(milliseconds: 200),
    this.onTap,
  });

  @override
  State<HoverAnimatedContainer> createState() => _HoverAnimatedContainerState();
}

class _HoverAnimatedContainerState extends State<HoverAnimatedContainer>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.hoverScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onEnter() {
    setState(() => _isHovered = true);
    _controller.forward();
  }

  void _onExit() {
    setState(() => _isHovered = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onEnter(),
      onExit: (_) => _onExit(),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: widget.child,
            );
          },
        ),
      ),
    );
  }
}

void main() {
  runApp(const BingoFeastApp());
}

class BingoFeastApp extends StatelessWidget {
  const BingoFeastApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Hatch \'n\' Wing Bingo Feast',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.orange),
        useMaterial3: true,
        fontFamily: 'Roboto',
      ),
      home: const BingoMenuScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class BingoMenuScreen extends StatefulWidget {
  const BingoMenuScreen({super.key});

  @override
  State<BingoMenuScreen> createState() => _BingoMenuScreenState();
}

class _BingoMenuScreenState extends State<BingoMenuScreen> {
  final DailyPlaysService _dailyPlaysService = DailyPlaysService();
  final String _userId = 'demo_user'; // In real app, get from auth

  final Map<BingoCardType, PlayAvailability> _playAvailability = {};

  @override
  void initState() {
    super.initState();
    _loadPlayAvailability();
  }

  Future<void> _loadPlayAvailability() async {
    final cardTypes = [
      BingoCardType.small5x5,
      BingoCardType.scratch3x3,
      BingoCardType.large8x8,
    ];

    for (final cardType in cardTypes) {
      final availability = await _dailyPlaysService.checkPlayAvailability(_userId, cardType);
      setState(() {
        _playAvailability[cardType] = availability;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isMobile = screenSize.width < 600;
    final isTablet = screenSize.width >= 600 && screenSize.width < 1024;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.orange.shade300,
              Colors.orange.shade500,
              Colors.red.shade400,
            ],
          ),
        ),
        child: SafeArea(
          child: SingleChildScrollView(
            padding: EdgeInsets.all(isMobile ? 16 : 20),
            child: Column(
              children: [
                SizedBox(height: isMobile ? 10 : 20),

                // Enhanced Header with Animation
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1000),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: 0.8 + (0.2 * value),
                      child: Opacity(
                        opacity: value,
                        child: HoverAnimatedContainer(
                          hoverScale: 1.02,
                          child: Container(
                            padding: EdgeInsets.all(isMobile ? 24 : 32),
                            constraints: BoxConstraints(
                              maxWidth: isMobile ? screenSize.width - 32 : 600,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white,
                                  Colors.orange.shade50,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(isMobile ? 20 : 24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.3),
                                  blurRadius: 40,
                                  offset: const Offset(0, 20),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Animated Chef Hat Icon
                                HoverAnimatedContainer(
                                  hoverScale: 1.1,
                                  child: Container(
                                    padding: EdgeInsets.all(isMobile ? 16 : 20),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [Colors.orange.shade400, Colors.red.shade400],
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.orange.withValues(alpha: 0.4),
                                          blurRadius: 15,
                                          offset: const Offset(0, 5),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.restaurant_menu,
                                      size: isMobile ? 36 : 48,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                SizedBox(height: isMobile ? 16 : 24),

                              // Enhanced Title
                              ShaderMask(
                                shaderCallback: (bounds) => LinearGradient(
                                  colors: [Colors.orange.shade700, Colors.red.shade600],
                                ).createShader(bounds),
                                child: Text(
                                  '🎯 Hatch \'n\' Wing\nBingo Feast',
                                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    height: 1.2,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Subtitle with Chef Bob
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  '👨‍🍳 Play delicious games with Chef Bob!',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.orange.shade800,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

                const SizedBox(height: 40),

                // Game mode buttons
              _buildGameModeButton(
                context,
                cardType: BingoCardType.small5x5,
                title: '5x5 Quick Game',
                subtitle: 'Fast 2-3 minute games',
                icon: Icons.grid_3x3,
                gameMode: GameMode.caller,
              ),

              const SizedBox(height: 16),

              _buildGameModeButton(
                context,
                cardType: BingoCardType.scratch3x3,
                title: '3x3 Scratch Card',
                subtitle: 'Instant win! Scratch 3 matching symbols',
                icon: Icons.touch_app,
                gameMode: GameMode.scratch,
              ),

              const SizedBox(height: 16),

              _buildGameModeButton(
                context,
                cardType: BingoCardType.large8x8,
                title: '8x8 Challenge',
                subtitle: 'Longer games, bigger rewards',
                icon: Icons.grid_4x4,
                gameMode: GameMode.caller,
              ),

              const SizedBox(height: 32),

                // Enhanced Info Section
                Container(
                  padding: const EdgeInsets.all(20),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        Colors.white.withValues(alpha: 0.9),
                        Colors.orange.shade50.withValues(alpha: 0.9),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 5),
                      ),
                    ],
                  ),
                  child: Column(
                    children: [
                      Icon(
                        Icons.stars,
                        color: Colors.orange.shade600,
                        size: 32,
                      ),
                      const SizedBox(height: 12),
                      Text(
                        'Earn Loyalty Points & Rewards!',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade800,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        '🎮 Play daily games\n💰 1000 points = \$1 off your order\n🎁 Win bonus rewards',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Colors.orange.shade700,
                          height: 1.5,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildGameModeButton(
    BuildContext context, {
    required BingoCardType cardType,
    required String title,
    required String subtitle,
    required IconData icon,
    required GameMode gameMode,
  }) {
    final availability = _playAvailability[cardType];
    final canPlay = availability?.canPlay ?? false;
    final statusText = availability?.statusMessage ?? 'Loading...';

    // Different color schemes for each game mode
    final colors = _getGameModeColors(cardType);

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 300),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.95 + (0.05 * value),
          child: HoverAnimatedContainer(
            hoverScale: 1.02,
            onTap: canPlay ? () => _handleGameStart(cardType, gameMode) : null,
            child: Container(
              margin: const EdgeInsets.symmetric(vertical: 8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: canPlay ? colors['gradient']! : [
                    Colors.grey.shade400,
                    Colors.grey.shade500,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: canPlay
                      ? colors['shadow']!.first.withValues(alpha: 0.4)
                      : Colors.grey.withValues(alpha: 0.3),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                  BoxShadow(
                    color: Colors.white.withValues(alpha: 0.1),
                    blurRadius: 1,
                    offset: const Offset(0, 1),
                  ),
                ],
              ),
              child: Padding(
                padding: const EdgeInsets.all(24),
                child: Row(
                  children: [
                    // Enhanced Icon with Background
                    HoverAnimatedContainer(
                      hoverScale: 1.1,
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: Colors.white.withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          icon,
                          size: 32,
                          color: Colors.white,
                        ),
                      ),
                    ),

                    const SizedBox(width: 20),

                    // Enhanced Text Content
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                              shadows: [
                                Shadow(
                                  color: Colors.black.withValues(alpha: 0.3),
                                  offset: const Offset(0, 1),
                                  blurRadius: 2,
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            subtitle,
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 8),

                          // Status Badge
                          HoverAnimatedContainer(
                            hoverScale: 1.05,
                            child: Container(
                              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                              decoration: BoxDecoration(
                                color: canPlay
                                  ? Colors.green.withValues(alpha: 0.2)
                                  : Colors.orange.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: canPlay
                                    ? Colors.green.withValues(alpha: 0.4)
                                    : Colors.orange.withValues(alpha: 0.4),
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                statusText,
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                  fontSize: 12,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Action Icon
                    HoverAnimatedContainer(
                      hoverScale: 1.15,
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Icon(
                          canPlay ? Icons.play_arrow : Icons.lock,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Map<String, List<Color>> _getGameModeColors(BingoCardType cardType) {
    switch (cardType) {
      case BingoCardType.small5x5:
        return {
          'gradient': [Colors.blue.shade400, Colors.blue.shade600],
          'shadow': [Colors.blue.shade400],
        };
      case BingoCardType.scratch3x3:
        return {
          'gradient': [Colors.purple.shade400, Colors.purple.shade600],
          'shadow': [Colors.purple.shade400],
        };
      case BingoCardType.large8x8:
        return {
          'gradient': [Colors.red.shade400, Colors.red.shade600],
          'shadow': [Colors.red.shade400],
        };
    }
  }

  Future<void> _handleGameStart(BingoCardType cardType, GameMode gameMode) async {
    final availability = _playAvailability[cardType];
    if (availability == null) return;

    if (availability.canPlayFree) {
      // Use free play
      final success = await _dailyPlaysService.recordFreePlay(_userId, cardType);
      if (success) {
        _startGame(cardType, gameMode);
        _loadPlayAvailability(); // Refresh availability
      }
    } else if (availability.canBuyExtra) {
      // Show purchase dialog
      _showPurchaseDialog(cardType, gameMode, availability.extraPlayCost);
    } else {
      // Show insufficient points dialog
      _showInsufficientPointsDialog(cardType, availability.extraPlayCost);
    }
  }

  void _startGame(BingoCardType cardType, GameMode gameMode) {
    // Generate a new bingo card
    final card = BingoCard.generate(
      userId: _userId,
      type: cardType,
      tier: BingoCardTier.bronze,
    );

    // Navigate to game screen
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => BingoGameScreen(
          bingoCard: card,
          gameMode: gameMode,
        ),
      ),
    );
  }

  void _showPurchaseDialog(BingoCardType cardType, GameMode gameMode, int cost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Extra Play - ${DailyPlays.getGameModeName(cardType)}'),
        content: Text(
          'You\'ve used your free play for today.\n\n'
          'Purchase an extra play for $cost points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await _dailyPlaysService.purchaseExtraPlay(_userId, cardType);
              if (mounted) {
                if (success) {
                  _startGame(cardType, gameMode);
                  _loadPlayAvailability(); // Refresh availability
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Extra play purchased! -$cost points')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Purchase failed. Please try again.')),
                  );
                }
              }
            },
            child: Text('Buy for $cost points'),
          ),
        ],
      ),
    );
  }

  void _showInsufficientPointsDialog(BingoCardType cardType, int cost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insufficient Points'),
        content: Text(
          'You need $cost points for an extra ${DailyPlays.getGameModeName(cardType)} play.\n\n'
          'Earn more points by:\n'
          '• Winning games\n'
          '• Daily check-ins\n'
          '• Making purchases',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
