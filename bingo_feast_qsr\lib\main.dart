// 🎮 Hatch 'n' Wing Bingo Feast - Main App
// This is the entry point for our bingo game

import 'package:flutter/material.dart';
import 'models/bingo_card.dart';
import 'models/game_state.dart';
import 'screens/bingo_game_screen.dart';
import 'screens/ar_bingo_screen.dart';
import 'services/daily_plays_service.dart';
import 'models/daily_plays.dart';
import 'widgets/hover_animated_container.dart';

// Reusable hover animation widget
class HoverAnimatedContainer extends StatefulWidget {
  final Widget child;
  final double hoverScale;
  final Duration duration;
  final VoidCallback? onTap;

  const HoverAnimatedContainer({
    super.key,
    required this.child,
    this.hoverScale = 1.05,
    this.duration = const Duration(milliseconds: 200),
    this.onTap,
  });

  @override
  State<HoverAnimatedContainer> createState() => _HoverAnimatedContainerState();
}

class _HoverAnimatedContainerState extends State<HoverAnimatedContainer>
    with SingleTickerProviderStateMixin {
  bool _isHovered = false;
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: widget.duration,
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: widget.hoverScale,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _onEnter() {
    setState(() => _isHovered = true);
    _controller.forward();
  }

  void _onExit() {
    setState(() => _isHovered = false);
    _controller.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => _onEnter(),
      onExit: (_) => _onExit(),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedBuilder(
          animation: _scaleAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: widget.child,
            );
          },
        ),
      ),
    );
  }
}

void main() {
  runApp(const BingoFeastApp());
}

class BingoFeastApp extends StatelessWidget {
  const BingoFeastApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Hatch \'n\' Wing Bingo Feast',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.orange),
        useMaterial3: true,
        fontFamily: 'Roboto',
      ),
      home: const BingoMenuScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

class BingoMenuScreen extends StatefulWidget {
  const BingoMenuScreen({super.key});

  @override
  State<BingoMenuScreen> createState() => _BingoMenuScreenState();
}

class _BingoMenuScreenState extends State<BingoMenuScreen> {
  final DailyPlaysService _dailyPlaysService = DailyPlaysService();
  final String _userId = 'demo_user'; // In real app, get from auth

  final Map<BingoCardType, PlayAvailability> _playAvailability = {};

  @override
  void initState() {
    super.initState();
    _loadPlayAvailability();
  }

  Future<void> _loadPlayAvailability() async {
    final cardTypes = [
      BingoCardType.small5x5,
      BingoCardType.scratch3x3,
      BingoCardType.large8x8,
    ];

    for (final cardType in cardTypes) {
      final availability = await _dailyPlaysService.checkPlayAvailability(_userId, cardType);
      setState(() {
        _playAvailability[cardType] = availability;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isMobile = screenSize.width < 600;
    final isTablet = screenSize.width >= 600 && screenSize.width < 1024;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF8BC34A), // Light green like inspiration
              const Color(0xFF4CAF50), // Medium green
              const Color(0xFF2E7D32), // Dark green
            ],
          ),
        ),
        child: Stack(
          children: [
            // Background decorative elements
            _buildBackgroundDecorations(),

            SafeArea(
              child: SingleChildScrollView(
                padding: EdgeInsets.all(isMobile ? 16 : 20),
                child: Column(
                  children: [
                    SizedBox(height: isMobile ? 10 : 20),

                // Enhanced Header with Animation
                TweenAnimationBuilder<double>(
                  duration: const Duration(milliseconds: 1000),
                  tween: Tween(begin: 0.0, end: 1.0),
                  builder: (context, value, child) {
                    return Transform.scale(
                      scale: 0.8 + (0.2 * value),
                      child: Opacity(
                        opacity: value,
                        child: HoverAnimatedContainer(
                          hoverScale: 1.02,
                          child: Container(
                            padding: EdgeInsets.all(isMobile ? 24 : 32),
                            constraints: BoxConstraints(
                              maxWidth: isMobile ? screenSize.width - 32 : 600,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  Colors.white,
                                  Colors.orange.shade50,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(isMobile ? 20 : 24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.2),
                                  blurRadius: 20,
                                  offset: const Offset(0, 10),
                                ),
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.3),
                                  blurRadius: 40,
                                  offset: const Offset(0, 20),
                                ),
                              ],
                            ),
                            child: Column(
                              children: [
                                // Animated Chef Hat Icon
                                HoverAnimatedContainer(
                                  hoverScale: 1.1,
                                  child: Container(
                                    padding: EdgeInsets.all(isMobile ? 16 : 20),
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [Colors.orange.shade400, Colors.red.shade400],
                                      ),
                                      shape: BoxShape.circle,
                                      boxShadow: [
                                        BoxShadow(
                                          color: Colors.orange.withValues(alpha: 0.4),
                                          blurRadius: 15,
                                          offset: const Offset(0, 5),
                                        ),
                                      ],
                                    ),
                                    child: Icon(
                                      Icons.restaurant_menu,
                                      size: isMobile ? 36 : 48,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                                SizedBox(height: isMobile ? 16 : 24),

                              // Enhanced Title
                              ShaderMask(
                                shaderCallback: (bounds) => LinearGradient(
                                  colors: [Colors.orange.shade700, Colors.red.shade600],
                                ).createShader(bounds),
                                child: Text(
                                  '🎯 Hatch \'n\' Wing\nBingo Feast',
                                  style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.white,
                                    height: 1.2,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              const SizedBox(height: 12),

                              // Subtitle with Chef Bob
                              Container(
                                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                decoration: BoxDecoration(
                                  color: Colors.orange.shade100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: Text(
                                  '� Earn loyalty points & rewards!\nPlay daily games, 1000 points = \$1, win bonus rewards.',
                                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                                    color: Colors.orange.shade800,
                                    fontWeight: FontWeight.w600,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                },
              ),

                const SizedBox(height: 40),

                // Modern Game Cards Layout
                _buildModernGameCards(context, isMobile),

                SizedBox(height: isMobile ? 24 : 32),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Background decorative elements
  Widget _buildBackgroundDecorations() {
    return Stack(
      children: [
        // Floating circles with animation
        Positioned(
          top: 100,
          right: 50,
          child: FloatingAnimation(
            duration: const Duration(seconds: 4),
            distance: 15,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.1),
              ),
            ),
          ),
        ),
        Positioned(
          top: 300,
          left: 30,
          child: FloatingAnimation(
            duration: const Duration(seconds: 3),
            distance: 10,
            child: Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.white.withValues(alpha: 0.05),
              ),
            ),
          ),
        ),
        // Plus signs with floating animation
        Positioned(
          top: 150,
          left: 80,
          child: FloatingAnimation(
            duration: const Duration(seconds: 2),
            distance: 8,
            child: Icon(
              Icons.add,
              color: Colors.white.withValues(alpha: 0.2),
              size: 24,
            ),
          ),
        ),
        Positioned(
          bottom: 200,
          right: 100,
          child: FloatingAnimation(
            duration: const Duration(seconds: 3),
            distance: 12,
            child: Icon(
              Icons.add,
              color: Colors.white.withValues(alpha: 0.15),
              size: 32,
            ),
          ),
        ),
      ],
    );
  }

  // Modern game cards layout
  Widget _buildModernGameCards(BuildContext context, bool isMobile) {
    return Column(
      children: [
        // Featured game card (like Tangled in inspiration)
        _buildFeaturedGameCard(context, isMobile),

        const SizedBox(height: 24),

        // Game mode cards grid
        _buildGameModeGrid(context, isMobile),
      ],
    );
  }

  // Featured game card (like the main Tangled card in inspiration)
  Widget _buildFeaturedGameCard(BuildContext context, bool isMobile) {
    final availability = _playAvailability[BingoCardType.small5x5];
    final canPlay = availability?.canPlay ?? false;

    return AnimatedGameCard(
      delay: const Duration(milliseconds: 200),
      child: HoverAnimatedContainer(
        hoverScale: 1.02,
        onTap: canPlay ? () => _handleGameStart(BingoCardType.small5x5, GameMode.caller) : null,
        child: Container(
          height: isMobile ? 200 : 240,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                const Color(0xFF4CAF50),
                const Color(0xFF2E7D32),
              ],
            ),
            borderRadius: BorderRadius.circular(24),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.2),
                blurRadius: 20,
                offset: const Offset(0, 10),
              ),
            ],
          ),
      child: Stack(
        children: [
          // Chef Bob character illustration placeholder
          Positioned(
            left: 20,
            top: 20,
            bottom: 20,
            child: Container(
              width: isMobile ? 120 : 140,
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.restaurant_menu,
                    size: isMobile ? 40 : 50,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '👨‍🍳\nChef Bob',
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isMobile ? 14 : 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Game info
          Positioned(
            right: 20,
            top: 20,
            bottom: 20,
            left: isMobile ? 160 : 180,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  'Bingo Feast',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: isMobile ? 24 : 28,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: Colors.black.withValues(alpha: 0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Play delicious bingo games\nwith Chef Bob!',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: isMobile ? 14 : 16,
                    height: 1.3,
                  ),
                ),
                const SizedBox(height: 16),

                // Play button
                GestureDetector(
                  onTap: canPlay ? () => _handleGameStart(BingoCardType.small5x5, GameMode.caller) : null,
                  child: Container(
                    padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
                    decoration: BoxDecoration(
                      color: Colors.orange,
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.orange.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Click to Start',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: isMobile ? 14 : 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.play_arrow,
                          color: Colors.white,
                          size: isMobile ? 18 : 20,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
        ),
      ),
    );
  }

  // Game mode grid (like the right side cards in inspiration)
  Widget _buildGameModeGrid(BuildContext context, bool isMobile) {
    return Column(
      children: [
        AnimatedGameCard(
          delay: const Duration(milliseconds: 400),
          child: _buildGameModeCard(
            context,
            cardType: BingoCardType.scratch3x3,
            title: '3x3 Scratch Card',
            subtitle: 'Instant win! Match 3 symbols',
            icon: '🎫',
            color: const Color(0xFFE91E63),
            isMobile: isMobile,
          ),
        ),
        const SizedBox(height: 16),
        AnimatedGameCard(
          delay: const Duration(milliseconds: 600),
          child: _buildGameModeCard(
            context,
            cardType: BingoCardType.large8x8,
            title: '8x8 Challenge',
            subtitle: 'Longer games, bigger rewards',
            icon: '🎯',
            color: const Color(0xFFFF9800),
            isMobile: isMobile,
          ),
        ),
        const SizedBox(height: 16),
        AnimatedGameCard(
          delay: const Duration(milliseconds: 800),
          child: _buildGameModeCard(
            context,
            cardType: BingoCardType.small5x5,
            title: '🥽 AR Bingo',
            subtitle: '3D Chef Bob & floating symbols!',
            icon: '🥽',
            color: const Color(0xFF9C27B0),
            gameMode: GameMode.ar,
            isSpecial: true,
            isMobile: isMobile,
          ),
        ),
      ],
    );
  }

  // Individual game mode card
  Widget _buildGameModeCard(
    BuildContext context, {
    required BingoCardType cardType,
    required String title,
    required String subtitle,
    required String icon,
    required Color color,
    required bool isMobile,
    GameMode gameMode = GameMode.caller,
    bool isSpecial = false,
  }) {
    final availability = _playAvailability[cardType];
    final canPlay = availability?.canPlay ?? false;
    final statusText = availability?.statusMessage ?? 'Loading...';

    return HoverAnimatedContainer(
      hoverScale: 1.03,
      onTap: canPlay ? () => _handleGameStart(cardType, gameMode) : null,
      child: Container(
        height: isMobile ? 90 : 110,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: canPlay ? [
              color.withValues(alpha: 0.8),
              color,
            ] : [
              Colors.grey.shade400,
              Colors.grey.shade500,
            ],
          ),
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: canPlay ? color.withValues(alpha: 0.3) : Colors.grey.withValues(alpha: 0.3),
              blurRadius: 12,
              offset: const Offset(0, 6),
            ),
          ],
        ),
        child: Stack(
          children: [
            // Character/Icon area
            Positioned(
              left: 16,
              top: 16,
              bottom: 16,
              child: Container(
                width: isMobile ? 60 : 70,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Center(
                  child: Text(
                    icon,
                    style: TextStyle(
                      fontSize: isMobile ? 24 : 28,
                    ),
                  ),
                ),
              ),
            ),

            // Game info
            Positioned(
              left: isMobile ? 90 : 100,
              right: 16,
              top: 16,
              bottom: 16,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isMobile ? 16 : 18,
                      fontWeight: FontWeight.bold,
                      shadows: [
                        Shadow(
                          color: Colors.black.withValues(alpha: 0.3),
                          offset: const Offset(0, 1),
                          blurRadius: 2,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: isMobile ? 11 : 13,
                    ),
                  ),
                  const SizedBox(height: 4),

                  // Status badge - only show if space allows
                  if (isMobile && statusText.length < 15 || !isMobile)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                      decoration: BoxDecoration(
                        color: canPlay
                          ? Colors.green.withValues(alpha: 0.2)
                          : Colors.orange.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(
                          color: canPlay
                            ? Colors.green.withValues(alpha: 0.4)
                            : Colors.orange.withValues(alpha: 0.4),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        statusText,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: isMobile ? 9 : 11,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Play arrow
            Positioned(
              right: 16,
              top: 16,
              child: Icon(
                canPlay ? Icons.play_arrow : Icons.lock,
                color: Colors.white.withValues(alpha: 0.8),
                size: isMobile ? 20 : 24,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _handleGameStart(BingoCardType cardType, GameMode gameMode) async {
    final availability = _playAvailability[cardType];
    if (availability == null) return;

    if (availability.canPlayFree) {
      // Use free play
      final success = await _dailyPlaysService.recordFreePlay(_userId, cardType);
      if (success) {
        _startGame(cardType, gameMode);
        _loadPlayAvailability(); // Refresh availability
      }
    } else if (availability.canBuyExtra) {
      // Show purchase dialog
      _showPurchaseDialog(cardType, gameMode, availability.extraPlayCost);
    } else {
      // Show insufficient points dialog
      _showInsufficientPointsDialog(cardType, availability.extraPlayCost);
    }
  }

  void _startGame(BingoCardType cardType, GameMode gameMode) {
    // Generate a new bingo card
    final card = BingoCard.generate(
      userId: _userId,
      type: cardType,
      tier: BingoCardTier.bronze,
    );

    // Navigate to appropriate game screen
    if (gameMode == GameMode.ar) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ARBingoScreen(
            bingoCard: card,
          ),
        ),
      );
    } else {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => BingoGameScreen(
            bingoCard: card,
            gameMode: gameMode,
          ),
        ),
      );
    }
  }

  void _showPurchaseDialog(BingoCardType cardType, GameMode gameMode, int cost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Extra Play - ${DailyPlays.getGameModeName(cardType)}'),
        content: Text(
          'You\'ve used your free play for today.\n\n'
          'Purchase an extra play for $cost points?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              final success = await _dailyPlaysService.purchaseExtraPlay(_userId, cardType);
              if (mounted) {
                if (success) {
                  _startGame(cardType, gameMode);
                  _loadPlayAvailability(); // Refresh availability
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('Extra play purchased! -$cost points')),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(content: Text('Purchase failed. Please try again.')),
                  );
                }
              }
            },
            child: Text('Buy for $cost points'),
          ),
        ],
      ),
    );
  }

  void _showInsufficientPointsDialog(BingoCardType cardType, int cost) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Insufficient Points'),
        content: Text(
          'You need $cost points for an extra ${DailyPlays.getGameModeName(cardType)} play.\n\n'
          'Earn more points by:\n'
          '• Winning games\n'
          '• Daily check-ins\n'
          '• Making purchases',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
