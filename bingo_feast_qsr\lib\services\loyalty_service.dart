// 🏆 Loyalty Service
// This manages the points system and integrates with the QSR app's loyalty program

import 'dart:async';
import 'package:cloud_firestore/cloud_firestore.dart';

// User loyalty profile
class LoyaltyProfile {
  final String userId;
  int totalPoints;
  int lifetimePoints;
  int gamesPlayed;
  int totalWins;
  DateTime lastPlayDate;
  List<String> recentRewards;
  Map<String, int> cardTypesPlayed;

  LoyaltyProfile({
    required this.userId,
    this.totalPoints = 0,
    this.lifetimePoints = 0,
    this.gamesPlayed = 0,
    this.totalWins = 0,
    DateTime? lastPlayDate,
    List<String>? recentRewards,
    Map<String, int>? cardTypesPlayed,
  }) : lastPlayDate = lastPlayDate ?? DateTime.now(),
       recentRewards = recentRewards ?? [],
       cardTypesPlayed = cardTypesPlayed ?? {};

  // Convert to/from JSON for Firebase
  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'totalPoints': totalPoints,
      'lifetimePoints': lifetimePoints,
      'gamesPlayed': gamesPlayed,
      'totalWins': totalWins,
      'lastPlayDate': lastPlayDate.toIso8601String(),
      'recentRewards': recentRewards,
      'cardTypesPlayed': cardTypesPlayed,
    };
  }

  factory LoyaltyProfile.fromJson(Map<String, dynamic> json) {
    return LoyaltyProfile(
      userId: json['userId'] ?? '',
      totalPoints: json['totalPoints'] ?? 0,
      lifetimePoints: json['lifetimePoints'] ?? 0,
      gamesPlayed: json['gamesPlayed'] ?? 0,
      totalWins: json['totalWins'] ?? 0,
      lastPlayDate: DateTime.parse(json['lastPlayDate'] ?? DateTime.now().toIso8601String()),
      recentRewards: List<String>.from(json['recentRewards'] ?? []),
      cardTypesPlayed: Map<String, int>.from(json['cardTypesPlayed'] ?? {}),
    );
  }

  // Calculate dollar value of points (1000 points = $1)
  double get dollarValue => totalPoints / 1000.0;

  // Get user level based on lifetime points
  String get userLevel {
    if (lifetimePoints < 1000) return 'Bronze';
    if (lifetimePoints < 5000) return 'Silver';
    if (lifetimePoints < 15000) return 'Gold';
    return 'Platinum';
  }

  // Get win rate percentage
  double get winRate {
    if (gamesPlayed == 0) return 0.0;
    return (totalWins / gamesPlayed) * 100;
  }
}

// Purchase tracking for card earning
class PurchaseTracker {
  final String userId;
  double weeklySpend;
  double totalSpend;
  DateTime weekStartDate;
  List<DateTime> checkIns;
  int referrals;

  PurchaseTracker({
    required this.userId,
    this.weeklySpend = 0.0,
    this.totalSpend = 0.0,
    DateTime? weekStartDate,
    List<DateTime>? checkIns,
    this.referrals = 0,
  }) : weekStartDate = weekStartDate ?? _getWeekStart(DateTime.now()),
       checkIns = checkIns ?? [];

  static DateTime _getWeekStart(DateTime date) {
    return date.subtract(Duration(days: date.weekday - 1));
  }

  // Check if user has earned cards this week
  Map<String, int> getEarnedCards() {
    Map<String, int> earnedCards = {};

    // 5x5 cards: $10 purchase = 1 card
    earnedCards['5x5_purchase'] = (totalSpend / 10).floor();

    // 8x8 cards: $50 weekly spend = 1 card
    earnedCards['8x8_weekly'] = (weeklySpend / 50).floor();

    // Check-in cards (1 per day, max 7 per week)
    final thisWeekCheckIns = checkIns.where((checkIn) =>
      checkIn.isAfter(weekStartDate) &&
      checkIn.isBefore(weekStartDate.add(const Duration(days: 7)))
    ).length;
    earnedCards['5x5_checkin'] = thisWeekCheckIns;

    // Referral cards (1 per referral)
    earnedCards['5x5_referral'] = referrals;

    return earnedCards;
  }

  Map<String, dynamic> toJson() {
    return {
      'userId': userId,
      'weeklySpend': weeklySpend,
      'totalSpend': totalSpend,
      'weekStartDate': weekStartDate.toIso8601String(),
      'checkIns': checkIns.map((date) => date.toIso8601String()).toList(),
      'referrals': referrals,
    };
  }

  factory PurchaseTracker.fromJson(Map<String, dynamic> json) {
    return PurchaseTracker(
      userId: json['userId'] ?? '',
      weeklySpend: json['weeklySpend']?.toDouble() ?? 0.0,
      totalSpend: json['totalSpend']?.toDouble() ?? 0.0,
      weekStartDate: DateTime.parse(json['weekStartDate'] ?? DateTime.now().toIso8601String()),
      checkIns: (json['checkIns'] as List?)?.map((date) => DateTime.parse(date)).toList() ?? [],
      referrals: json['referrals'] ?? 0,
    );
  }
}

class LoyaltyService {
  static final LoyaltyService _instance = LoyaltyService._internal();
  factory LoyaltyService() => _instance;
  LoyaltyService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final StreamController<LoyaltyProfile> _profileController = StreamController<LoyaltyProfile>.broadcast();

  // Stream for real-time loyalty updates
  Stream<LoyaltyProfile> get profileStream => _profileController.stream;

  // Get user's loyalty profile
  Future<LoyaltyProfile> getLoyaltyProfile(String userId) async {
    try {
      final doc = await _firestore.collection('loyalty_profiles').doc(userId).get();

      if (doc.exists) {
        return LoyaltyProfile.fromJson(doc.data()!);
      } else {
        // Create new profile for first-time user
        final newProfile = LoyaltyProfile(userId: userId);
        await _saveLoyaltyProfile(newProfile);
        return newProfile;
      }
    } catch (e) {
      print('Error getting loyalty profile: $e');
      return LoyaltyProfile(userId: userId);
    }
  }

  // Save loyalty profile to Firebase
  Future<void> _saveLoyaltyProfile(LoyaltyProfile profile) async {
    try {
      await _firestore.collection('loyalty_profiles').doc(profile.userId).set(profile.toJson());
      _profileController.add(profile);
    } catch (e) {
      print('Error saving loyalty profile: $e');
    }
  }

  // Award points for game completion
  Future<void> awardPoints(String userId, int points, String reason) async {
    try {
      final profile = await getLoyaltyProfile(userId);

      profile.totalPoints += points;
      profile.lifetimePoints += points;
      profile.recentRewards.add('$points points - $reason');

      // Keep only last 10 rewards
      if (profile.recentRewards.length > 10) {
        profile.recentRewards = profile.recentRewards.sublist(profile.recentRewards.length - 10);
      }

      await _saveLoyaltyProfile(profile);
    } catch (e) {
      print('Error awarding points: $e');
    }
  }

  // Record game completion
  Future<void> recordGameCompletion(String userId, bool won, String cardType) async {
    try {
      final profile = await getLoyaltyProfile(userId);

      profile.gamesPlayed++;
      if (won) profile.totalWins++;
      profile.lastPlayDate = DateTime.now();

      // Track card types played
      profile.cardTypesPlayed[cardType] = (profile.cardTypesPlayed[cardType] ?? 0) + 1;

      await _saveLoyaltyProfile(profile);
    } catch (e) {
      print('Error recording game completion: $e');
    }
  }

  // Redeem points for discount
  Future<bool> redeemPoints(String userId, int pointsToRedeem) async {
    try {
      final profile = await getLoyaltyProfile(userId);

      if (profile.totalPoints >= pointsToRedeem) {
        profile.totalPoints -= pointsToRedeem;
        profile.recentRewards.add('Redeemed $pointsToRedeem points');

        await _saveLoyaltyProfile(profile);
        return true;
      }
      return false;
    } catch (e) {
      print('Error redeeming points: $e');
      return false;
    }
  }

  // Get purchase tracker for card earning
  Future<PurchaseTracker> getPurchaseTracker(String userId) async {
    try {
      final doc = await _firestore.collection('purchase_trackers').doc(userId).get();

      if (doc.exists) {
        return PurchaseTracker.fromJson(doc.data()!);
      } else {
        final newTracker = PurchaseTracker(userId: userId);
        await _savePurchaseTracker(newTracker);
        return newTracker;
      }
    } catch (e) {
      print('Error getting purchase tracker: $e');
      return PurchaseTracker(userId: userId);
    }
  }

  // Save purchase tracker
  Future<void> _savePurchaseTracker(PurchaseTracker tracker) async {
    try {
      await _firestore.collection('purchase_trackers').doc(tracker.userId).set(tracker.toJson());
    } catch (e) {
      print('Error saving purchase tracker: $e');
    }
  }

  // Record a purchase (for card earning)
  Future<void> recordPurchase(String userId, double amount) async {
    try {
      final tracker = await getPurchaseTracker(userId);

      tracker.totalSpend += amount;

      // Check if it's a new week
      final currentWeekStart = PurchaseTracker._getWeekStart(DateTime.now());
      if (tracker.weekStartDate.isBefore(currentWeekStart)) {
        tracker.weeklySpend = amount; // Reset weekly spend
        tracker.weekStartDate = currentWeekStart;
      } else {
        tracker.weeklySpend += amount;
      }

      await _savePurchaseTracker(tracker);
    } catch (e) {
      print('Error recording purchase: $e');
    }
  }

  // Record check-in
  Future<void> recordCheckIn(String userId) async {
    try {
      final tracker = await getPurchaseTracker(userId);
      final today = DateTime.now();

      // Check if already checked in today
      final alreadyCheckedIn = tracker.checkIns.any((checkIn) =>
        checkIn.year == today.year &&
        checkIn.month == today.month &&
        checkIn.day == today.day
      );

      if (!alreadyCheckedIn) {
        tracker.checkIns.add(today);
        await _savePurchaseTracker(tracker);
      }
    } catch (e) {
      print('Error recording check-in: $e');
    }
  }

  // Record referral
  Future<void> recordReferral(String userId) async {
    try {
      final tracker = await getPurchaseTracker(userId);
      tracker.referrals++;
      await _savePurchaseTracker(tracker);
    } catch (e) {
      print('Error recording referral: $e');
    }
  }

  // Get leaderboard (top players by lifetime points)
  Future<List<LoyaltyProfile>> getLeaderboard({int limit = 10}) async {
    try {
      final query = await _firestore
          .collection('loyalty_profiles')
          .orderBy('lifetimePoints', descending: true)
          .limit(limit)
          .get();

      return query.docs.map((doc) => LoyaltyProfile.fromJson(doc.data())).toList();
    } catch (e) {
      print('Error getting leaderboard: $e');
      return [];
    }
  }

  // Dispose resources
  void dispose() {
    _profileController.close();
  }
}