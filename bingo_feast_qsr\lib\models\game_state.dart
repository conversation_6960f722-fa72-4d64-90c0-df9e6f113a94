// 🎮 Game State Model
// This manages the current game session and calling sequence

import 'dart:async';
import 'dart:math';
import 'food_symbol.dart';
import 'bingo_card.dart';

// Game modes
enum GameMode {
  caller,   // Chef Bob calls symbols
  scratch,  // Player reveals symbols manually
  ar,       // Augmented Reality mode with 3D Chef Bob
}

// Game status
enum GameStatus {
  waiting,    // Waiting to start
  playing,    // Game in progress
  paused,     // Game paused
  completed,  // Game finished
}

// Reward types
enum RewardType {
  none,
  points,
  freeItem,
  discount,
  bonusCard,
  giftCard,
}

// Reward information
class GameReward {
  final RewardType type;
  final String description;
  final int value;        // Points or dollar amount
  final String? itemId;   // For free items
  final String? code;     // QR code or promo code

  const GameReward({
    required this.type,
    required this.description,
    required this.value,
    this.itemId,
    this.code,
  });

  Map<String, dynamic> toJson() {
    return {
      'type': type.name,
      'description': description,
      'value': value,
      'itemId': itemId,
      'code': code,
    };
  }

  factory GameReward.fromJson(Map<String, dynamic> json) {
    return GameReward(
      type: RewardType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => RewardType.none,
      ),
      description: json['description'] ?? '',
      value: json['value'] ?? 0,
      itemId: json['itemId'],
      code: json['code'],
    );
  }
}

// Main game state class
class GameState {
  final String gameId;
  final BingoCard card;
  final GameMode mode;
  GameStatus status;

  // Calling sequence
  List<FoodSymbol> callingSequence;
  int currentCallIndex;
  FoodSymbol? currentSymbol;
  Timer? callTimer;

  // Game timing
  DateTime? startTime;
  DateTime? endTime;
  Duration? gameDuration;

  // Results
  bool hasWon;
  GameReward? reward;
  int pointsEarned;

  // Settings
  int callIntervalSeconds;  // Time between calls
  int maxCalls;            // Maximum number of calls

  GameState({
    required this.gameId,
    required this.card,
    required this.mode,
    this.status = GameStatus.waiting,
    this.callingSequence = const [],
    this.currentCallIndex = 0,
    this.currentSymbol,
    this.hasWon = false,
    this.pointsEarned = 0,
    this.callIntervalSeconds = 7, // Default 7 seconds between calls
    int? maxCalls,
  }) : maxCalls = maxCalls ?? (card.type == BingoCardType.small5x5 ? 10 : 20);

  // Generate calling sequence for caller mode
  void generateCallingSequence() {
    if (mode != GameMode.caller) return;

    // Create a completely new mutable list to be extra safe
    final allSymbols = <FoodSymbol>[];
    allSymbols.addAll(FoodSymbols.mainItems);
    allSymbols.addAll(FoodSymbols.sideItems);
    allSymbols.addAll(FoodSymbols.drinks);
    allSymbols.addAll(FoodSymbols.desserts);

    // Now safe to shuffle
    allSymbols.shuffle();

    // Take only the number we need
    callingSequence = allSymbols.take(maxCalls).toList();
    currentCallIndex = 0;
  }

  // Start the game
  void startGame() {
    if (status != GameStatus.waiting) return;

    status = GameStatus.playing;
    startTime = DateTime.now();

    // Don't start calling here - let the UI handle it
    // The UI will call generateCallingSequence() when ready
  }

  // Start the calling timer
  void _startCalling() {
    if (callingSequence.isEmpty) return;

    callTimer = Timer.periodic(
      Duration(seconds: callIntervalSeconds),
      (timer) {
        if (currentCallIndex < callingSequence.length && status == GameStatus.playing) {
          _makeCall();
        } else {
          _endGame();
        }
      },
    );

    // Make the first call immediately
    _makeCall();
  }

  // Make a call (announce next symbol)
  void _makeCall() {
    if (currentCallIndex >= callingSequence.length) {
      _endGame();
      return;
    }

    currentSymbol = callingSequence[currentCallIndex];

    // Mark matching symbols on the card
    final markedPositions = card.markSymbol(currentSymbol!);

    // Check for win after each call
    if (card.checkForWin()) {
      _endGame();
      return;
    }

    currentCallIndex++;
  }

  // End the game
  void _endGame() {
    callTimer?.cancel();
    status = GameStatus.completed;
    endTime = DateTime.now();

    if (startTime != null && endTime != null) {
      gameDuration = endTime!.difference(startTime!);
    }

    // Calculate final results
    _calculateResults();
  }

  // Calculate game results and rewards
  void _calculateResults() {
    if (card.hasWon) {
      hasWon = true;
      reward = _generateReward();
      pointsEarned = reward?.value ?? 0;
    } else {
      // Consolation points for playing
      pointsEarned = card.type == BingoCardType.small5x5 ? 5 : 10;
      reward = GameReward(
        type: RewardType.points,
        description: 'Thanks for playing! Here are some points.',
        value: pointsEarned,
      );
    }

    card.pointsEarned = pointsEarned;
    card.isCompleted = true;
  }

  // Generate reward based on card type and tier
  GameReward _generateReward() {
    final random = Random();
    final isSmallCard = card.type == BingoCardType.small5x5;

    // Win rate: 60-70% for 5x5, 40% for 8x8
    final winThreshold = isSmallCard ? 0.65 : 0.40;

    if (isSmallCard) {
      // 5x5 card rewards
      final rewardRoll = random.nextDouble();

      if (rewardRoll < 0.50) {
        // 50% small wins
        return _generateSmallReward();
      } else if (rewardRoll < 0.65) {
        // 15% medium wins
        return _generateMediumReward();
      } else {
        // 5% big wins
        return _generateBigReward();
      }
    } else {
      // 8x8 card rewards
      final rewardRoll = random.nextDouble();

      if (rewardRoll < 0.30) {
        // 30% small wins
        return _generateSmallReward();
      } else if (rewardRoll < 0.38) {
        // 8% medium wins
        return _generateMediumReward();
      } else {
        // 2% big wins
        return _generateBigReward();
      }
    }
  }

  // Generate small rewards (10-50 points, free side items, 10% off)
  GameReward _generateSmallReward() {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Points reward
        final points = 10 + random.nextInt(41); // 10-50 points
        return GameReward(
          type: RewardType.points,
          description: 'You earned $points loyalty points!',
          value: points,
        );
      case 1:
        // Free side item
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free French Fries!',
          value: 0,
          itemId: 'fries_regular',
          code: 'BINGO${random.nextInt(10000).toString().padLeft(4, '0')}',
        );
      default:
        // 10% discount
        return GameReward(
          type: RewardType.discount,
          description: '10% off your next order!',
          value: 10,
          code: 'SAVE10${random.nextInt(1000).toString().padLeft(3, '0')}',
        );
    }
  }

  // Generate medium rewards (free combo items, 20% off, bonus cards)
  GameReward _generateMediumReward() {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Free combo item
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free Classic Burger!',
          value: 0,
          itemId: 'burger_classic',
          code: 'BURGER${random.nextInt(10000).toString().padLeft(4, '0')}',
        );
      case 1:
        // 20% discount
        return GameReward(
          type: RewardType.discount,
          description: '20% off your next order!',
          value: 20,
          code: 'SAVE20${random.nextInt(1000).toString().padLeft(3, '0')}',
        );
      default:
        // Bonus card
        return GameReward(
          type: RewardType.bonusCard,
          description: 'Free bonus bingo card!',
          value: 1,
          code: 'BONUS${random.nextInt(1000).toString().padLeft(3, '0')}',
        );
    }
  }

  // Generate big rewards (free combo meals, $10 gift cards, exclusive items)
  GameReward _generateBigReward() {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Free combo meal
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free Combo Meal!',
          value: 0,
          itemId: 'combo_meal',
          code: 'COMBO${random.nextInt(10000).toString().padLeft(4, '0')}',
        );
      case 1:
        // $10 gift card
        return GameReward(
          type: RewardType.giftCard,
          description: '\$10 Gift Card!',
          value: 10,
          code: 'GIFT${random.nextInt(10000).toString().padLeft(4, '0')}',
        );
      default:
        // Exclusive item
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free Exclusive Wings Platter!',
          value: 0,
          itemId: 'wings_exclusive',
          code: 'WINGS${random.nextInt(10000).toString().padLeft(4, '0')}',
        );
    }
  }

  // Pause the game
  void pauseGame() {
    if (status == GameStatus.playing) {
      status = GameStatus.paused;
      callTimer?.cancel();
    }
  }

  // Resume the game
  void resumeGame() {
    if (status == GameStatus.paused) {
      status = GameStatus.playing;
      if (mode == GameMode.caller) {
        _startCalling();
      }
    }
  }

  // Handle manual square reveal (scratch mode)
  void revealSquare(int row, int col) {
    if (mode != GameMode.scratch || status != GameStatus.playing) return;

    card.markSquare(row, col, isCallerMode: false);

    // Check for win after each reveal
    if (card.checkForWin()) {
      _endGame();
    }
  }

  // Get game progress (0.0 to 1.0)
  double get progress {
    if (mode == GameMode.caller) {
      return currentCallIndex / maxCalls;
    } else {
      return card.progress;
    }
  }

  // Get time remaining (for caller mode)
  Duration? get timeRemaining {
    if (mode != GameMode.caller || status != GameStatus.playing) return null;

    final callsRemaining = maxCalls - currentCallIndex;
    return Duration(seconds: callsRemaining * callIntervalSeconds);
  }

  // Clean up resources
  void dispose() {
    callTimer?.cancel();
  }

  // Convert to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'gameId': gameId,
      'card': card.toJson(),
      'mode': mode.name,
      'status': status.name,
      'callingSequence': callingSequence.map((s) => s.toJson()).toList(),
      'currentCallIndex': currentCallIndex,
      'currentSymbol': currentSymbol?.toJson(),
      'startTime': startTime?.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'hasWon': hasWon,
      'reward': reward?.toJson(),
      'pointsEarned': pointsEarned,
      'callIntervalSeconds': callIntervalSeconds,
      'maxCalls': maxCalls,
    };
  }

  factory GameState.fromJson(Map<String, dynamic> json) {
    return GameState(
      gameId: json['gameId'] ?? '',
      card: BingoCard.fromJson(json['card']),
      mode: GameMode.values.firstWhere(
        (e) => e.name == json['mode'],
        orElse: () => GameMode.caller,
      ),
      status: GameStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => GameStatus.waiting,
      ),
      callingSequence: (json['callingSequence'] as List?)
          ?.map((s) => FoodSymbol.fromJson(s))
          .toList() ?? [],
      currentCallIndex: json['currentCallIndex'] ?? 0,
      hasWon: json['hasWon'] ?? false,
      pointsEarned: json['pointsEarned'] ?? 0,
      callIntervalSeconds: json['callIntervalSeconds'] ?? 7,
      maxCalls: json['maxCalls'],
    )..currentSymbol = json['currentSymbol'] != null
        ? FoodSymbol.fromJson(json['currentSymbol'])
        : null
     ..reward = json['reward'] != null
        ? GameReward.fromJson(json['reward'])
        : null;
  }

  @override
  String toString() {
    return 'GameState(gameId: $gameId, mode: $mode, status: $status, hasWon: $hasWon)';
  }
}