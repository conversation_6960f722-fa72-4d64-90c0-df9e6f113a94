// 🍔 Food Symbol Model
// This represents each food item that can appear on bingo cards

class FoodSymbol {
  final String id;           // Unique identifier (like "burger_001")
  final String name;         // Display name (like "Burger")
  final String imagePath;    // Path to the food image
  final String soundPath;    // Path to the sound file when called
  final String category;     // Food category (main, side, drink, dessert)
  final bool isSpecial;      // Special symbols for bonus patterns

  const FoodSymbol({
    required this.id,
    required this.name,
    required this.imagePath,
    required this.soundPath,
    required this.category,
    this.isSpecial = false,
  });

  // Convert to/from JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'imagePath': imagePath,
      'soundPath': soundPath,
      'category': category,
      'isSpecial': isSpecial,
    };
  }

  factory FoodSymbol.fromJson(Map<String, dynamic> json) {
    return FoodSymbol(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      imagePath: json['imagePath'] ?? '',
      soundPath: json['soundPath'] ?? '',
      category: json['category'] ?? '',
      isSpecial: json['isSpecial'] ?? false,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is FoodSymbol && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() => 'FoodSymbol(id: $id, name: $name)';
}

// 🍟 Predefined Food Symbols for QSR
class FoodSymbols {
  // Main items (burgers, chicken, etc.)
  static const List<FoodSymbol> mainItems = [
    FoodSymbol(
      id: 'burger_classic',
      name: 'Classic Burger',
      imagePath: 'assets/food_symbols/burger_classic.png',
      soundPath: 'assets/sounds/burger.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'chicken_crispy',
      name: 'Crispy Chicken',
      imagePath: 'assets/food_symbols/chicken_crispy.png',
      soundPath: 'assets/sounds/chicken.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'chicken_wings',
      name: 'Chicken Wings',
      imagePath: 'assets/food_symbols/chicken_wings.png',
      soundPath: 'assets/sounds/wings.mp3',
      category: 'main',
      isSpecial: true, // Special for wing-themed patterns
    ),
    FoodSymbol(
      id: 'fish_sandwich',
      name: 'Fish Sandwich',
      imagePath: 'assets/food_symbols/fish_sandwich.png',
      soundPath: 'assets/sounds/fish.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'hot_dog',
      name: 'Hot Dog',
      imagePath: 'assets/food_symbols/hot_dog.png',
      soundPath: 'assets/sounds/hotdog.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'pizza_slice',
      name: 'Pizza Slice',
      imagePath: 'assets/food_symbols/pizza_slice.png',
      soundPath: 'assets/sounds/pizza.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'taco',
      name: 'Taco',
      imagePath: 'assets/food_symbols/taco.png',
      soundPath: 'assets/sounds/taco.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'sandwich',
      name: 'Sandwich',
      imagePath: 'assets/food_symbols/sandwich.png',
      soundPath: 'assets/sounds/sandwich.mp3',
      category: 'main',
    ),
    FoodSymbol(
      id: 'salad',
      name: 'Salad',
      imagePath: 'assets/food_symbols/salad.png',
      soundPath: 'assets/sounds/salad.mp3',
      category: 'main',
    ),
  ];

  // Side items
  static const List<FoodSymbol> sideItems = [
    FoodSymbol(
      id: 'fries_regular',
      name: 'French Fries',
      imagePath: 'assets/food_symbols/fries_regular.png',
      soundPath: 'assets/sounds/fries.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'onion_rings',
      name: 'Onion Rings',
      imagePath: 'assets/food_symbols/onion_rings.png',
      soundPath: 'assets/sounds/onion_rings.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'mozzarella_sticks',
      name: 'Mozzarella Sticks',
      imagePath: 'assets/food_symbols/mozzarella_sticks.png',
      soundPath: 'assets/sounds/mozzarella.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'coleslaw',
      name: 'Coleslaw',
      imagePath: 'assets/food_symbols/coleslaw.png',
      soundPath: 'assets/sounds/coleslaw.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'mac_cheese',
      name: 'Mac & Cheese',
      imagePath: 'assets/food_symbols/mac_cheese.png',
      soundPath: 'assets/sounds/mac_cheese.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'baked_beans',
      name: 'Baked Beans',
      imagePath: 'assets/food_symbols/baked_beans.png',
      soundPath: 'assets/sounds/beans.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'corn',
      name: 'Corn',
      imagePath: 'assets/food_symbols/corn.png',
      soundPath: 'assets/sounds/corn.mp3',
      category: 'side',
    ),
    FoodSymbol(
      id: 'potato_wedges',
      name: 'Potato Wedges',
      imagePath: 'assets/food_symbols/potato_wedges.png',
      soundPath: 'assets/sounds/wedges.mp3',
      category: 'side',
    ),
  ];

  // Drinks
  static const List<FoodSymbol> drinks = [
    FoodSymbol(
      id: 'soda_cola',
      name: 'Cola',
      imagePath: 'assets/food_symbols/soda_cola.png',
      soundPath: 'assets/sounds/soda.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'milkshake',
      name: 'Milkshake',
      imagePath: 'assets/food_symbols/milkshake.png',
      soundPath: 'assets/sounds/milkshake.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'lemonade',
      name: 'Lemonade',
      imagePath: 'assets/food_symbols/lemonade.png',
      soundPath: 'assets/sounds/lemonade.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'coffee',
      name: 'Coffee',
      imagePath: 'assets/food_symbols/coffee.png',
      soundPath: 'assets/sounds/coffee.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'orange_juice',
      name: 'Orange Juice',
      imagePath: 'assets/food_symbols/orange_juice.png',
      soundPath: 'assets/sounds/orange_juice.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'water',
      name: 'Water',
      imagePath: 'assets/food_symbols/water.png',
      soundPath: 'assets/sounds/water.mp3',
      category: 'drink',
    ),
    FoodSymbol(
      id: 'tea',
      name: 'Iced Tea',
      imagePath: 'assets/food_symbols/tea.png',
      soundPath: 'assets/sounds/tea.mp3',
      category: 'drink',
    ),
  ];

  // Desserts
  static const List<FoodSymbol> desserts = [
    FoodSymbol(
      id: 'ice_cream',
      name: 'Ice Cream',
      imagePath: 'assets/food_symbols/ice_cream.png',
      soundPath: 'assets/sounds/ice_cream.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'apple_pie',
      name: 'Apple Pie',
      imagePath: 'assets/food_symbols/apple_pie.png',
      soundPath: 'assets/sounds/pie.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'chocolate_cake',
      name: 'Chocolate Cake',
      imagePath: 'assets/food_symbols/chocolate_cake.png',
      soundPath: 'assets/sounds/cake.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'cookies',
      name: 'Cookies',
      imagePath: 'assets/food_symbols/cookies.png',
      soundPath: 'assets/sounds/cookies.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'donut',
      name: 'Donut',
      imagePath: 'assets/food_symbols/donut.png',
      soundPath: 'assets/sounds/donut.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'brownie',
      name: 'Brownie',
      imagePath: 'assets/food_symbols/brownie.png',
      soundPath: 'assets/sounds/brownie.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'muffin',
      name: 'Muffin',
      imagePath: 'assets/food_symbols/muffin.png',
      soundPath: 'assets/sounds/muffin.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'cheesecake',
      name: 'Cheesecake',
      imagePath: 'assets/food_symbols/cheesecake.png',
      soundPath: 'assets/sounds/cheesecake.mp3',
      category: 'dessert',
    ),
    FoodSymbol(
      id: 'pudding',
      name: 'Pudding',
      imagePath: 'assets/food_symbols/pudding.png',
      soundPath: 'assets/sounds/pudding.mp3',
      category: 'dessert',
    ),
  ];

  // Get all symbols combined (as a new mutable list)
  static List<FoodSymbol> get allSymbols {
    return List<FoodSymbol>.from([
      ...mainItems,
      ...sideItems,
      ...drinks,
      ...desserts,
    ]);
  }

  // Get random symbols for card generation
  static List<FoodSymbol> getRandomSymbols(int count) {
    // Create a completely new mutable list
    final symbols = <FoodSymbol>[];
    symbols.addAll(mainItems);
    symbols.addAll(sideItems);
    symbols.addAll(drinks);
    symbols.addAll(desserts);

    // Now safe to shuffle
    symbols.shuffle();

    // If we need more symbols than available, repeat some
    if (count > symbols.length) {
      final needed = count - symbols.length;
      final extraSymbols = List<FoodSymbol>.from(symbols.take(needed));
      symbols.addAll(extraSymbols);
      symbols.shuffle();
    }

    return symbols.take(count).toList();
  }

  // Find symbol by ID
  static FoodSymbol? findById(String id) {
    try {
      return allSymbols.firstWhere((symbol) => symbol.id == id);
    } catch (e) {
      return null;
    }
  }

  // Get symbols by category
  static List<FoodSymbol> getByCategory(String category) {
    return allSymbols.where((symbol) => symbol.category == category).toList();
  }

  // Get special symbols (for bonus patterns)
  static List<FoodSymbol> get specialSymbols {
    return allSymbols.where((symbol) => symbol.isSpecial).toList();
  }
}