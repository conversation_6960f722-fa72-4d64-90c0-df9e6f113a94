import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../models/food_symbol.dart';

/// Utility class to generate placeholder food images
/// This creates beautiful placeholder images that can be replaced with real photos later
class FoodImageGenerator {
  
  /// Generate a placeholder image for a food symbol
  static Future<Uint8List> generatePlaceholderImage({
    required FoodSymbol symbol,
    int width = 128,
    int height = 128,
  }) async {
    final recorder = ui.PictureRecorder();
    final canvas = <PERSON>vas(recorder);
    final size = Size(width.toDouble(), height.toDouble());
    
    // Background gradient
    final backgroundPaint = Paint()
      ..shader = LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: _getGradientColors(symbol.category),
      ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));
    
    // Draw background
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(12),
      ),
      backgroundPaint,
    );
    
    // Draw emoji
    final emoji = _getFoodEmoji(symbol.id);
    final textPainter = TextPainter(
      text: TextSpan(
        text: emoji,
        style: TextStyle(
          fontSize: width * 0.5,
          fontFamily: 'Noto Color Emoji',
        ),
      ),
      textDirection: TextDirection.ltr,
    );
    
    textPainter.layout();
    textPainter.paint(
      canvas,
      Offset(
        (size.width - textPainter.width) / 2,
        (size.height - textPainter.height) / 2,
      ),
    );
    
    // Draw border
    final borderPaint = Paint()
      ..color = Colors.white.withValues(alpha: 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;
    
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        Rect.fromLTWH(0, 0, size.width, size.height),
        const Radius.circular(12),
      ),
      borderPaint,
    );
    
    final picture = recorder.endRecording();
    final image = await picture.toImage(width, height);
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
    
    return byteData!.buffer.asUint8List();
  }
  
  /// Get gradient colors based on food category
  static List<Color> _getGradientColors(String category) {
    switch (category) {
      case 'main':
        return [Colors.orange.shade200, Colors.red.shade300];
      case 'side':
        return [Colors.yellow.shade200, Colors.orange.shade300];
      case 'drink':
        return [Colors.blue.shade200, Colors.cyan.shade300];
      case 'dessert':
        return [Colors.pink.shade200, Colors.purple.shade300];
      default:
        return [Colors.grey.shade200, Colors.grey.shade400];
    }
  }
  
  /// Get emoji for food symbol (same as in FoodImageWidget)
  static String _getFoodEmoji(String symbolId) {
    switch (symbolId) {
      // Main items
      case 'burger_classic': return '🍔';
      case 'chicken_crispy': return '🍗';
      case 'chicken_wings': return '🍖';
      case 'fish_sandwich': return '🐟';
      case 'hot_dog': return '🌭';
      case 'pizza_slice': return '🍕';
      case 'taco': return '🌮';
      case 'sandwich': return '🥪';
      case 'salad': return '🥗';
      
      // Side items
      case 'fries_regular': return '🍟';
      case 'onion_rings': return '🧅';
      case 'mozzarella_sticks': return '🧀';
      case 'coleslaw': return '🥬';
      case 'mac_cheese': return '🧀';
      case 'baked_beans': return '🫘';
      case 'corn': return '🌽';
      case 'potato_wedges': return '🥔';
      
      // Drinks
      case 'soda_cola': return '🥤';
      case 'milkshake': return '🥛';
      case 'lemonade': return '🍋';
      case 'coffee': return '☕';
      case 'orange_juice': return '🍊';
      case 'water': return '💧';
      case 'tea': return '🧊';
      
      // Desserts
      case 'ice_cream': return '🍦';
      case 'apple_pie': return '🥧';
      case 'chocolate_cake': return '🍰';
      case 'cookies': return '🍪';
      case 'donut': return '🍩';
      case 'brownie': return '🟫';
      case 'muffin': return '🧁';
      case 'cheesecake': return '🍰';
      case 'pudding': return '🍮';
      
      default: return '🍽️';
    }
  }
  
  /// Generate all placeholder images and save them to assets folder
  static Future<void> generateAllPlaceholders() async {
    final symbols = FoodSymbols.allSymbols;
    
    for (final symbol in symbols) {
      try {
        final imageData = await generatePlaceholderImage(symbol: symbol);
        
        // In a real app, you would save this to the assets folder
        // For now, we'll just use the emoji approach in FoodImageWidget
        print('Generated placeholder for ${symbol.name}');
      } catch (e) {
        print('Error generating placeholder for ${symbol.name}: $e');
      }
    }
  }
}
