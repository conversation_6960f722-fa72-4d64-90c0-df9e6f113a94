// 🎯 Bingo Card Model
// This represents a single bingo card with food symbols

import 'dart:math';
import 'food_symbol.dart';

// Individual square on the bingo card
class BingoSquare {
  final FoodSymbol symbol;
  bool isMarked;
  bool isRevealed; // For scratch mode

  BingoSquare({
    required this.symbol,
    this.isMarked = false,
    this.isRevealed = false,
  });

  // Convert to/from JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'symbol': symbol.toJson(),
      'isMarked': isMarked,
      'isRevealed': isRevealed,
    };
  }

  factory BingoSquare.fromJson(Map<String, dynamic> json) {
    return BingoSquare(
      symbol: FoodSymbol.fromJson(json['symbol']),
      isMarked: json['isMarked'] ?? false,
      isRevealed: json['isRevealed'] ?? false,
    );
  }

  BingoSquare copyWith({
    FoodSymbol? symbol,
    bool? isMarked,
    bool? isRevealed,
  }) {
    return BingoSquare(
      symbol: symbol ?? this.symbol,
      isMarked: isMarked ?? this.isMarked,
      isRevealed: isRevealed ?? this.isRevealed,
    );
  }
}

// Types of bingo cards
enum BingoCardType {
  scratch3x3, // 9 squares - instant win scratch cards
  small5x5,   // 25 squares - quick games
  large8x8,   // 64 squares - challenge games
}

// Card tiers for different rewards
enum BingoCardTier {
  bronze,  // Basic rewards
  silver,  // Better rewards
  gold,    // Best rewards
}

// Main bingo card class
class BingoCard {
  final String id;                    // Unique card ID
  final BingoCardType type;           // 5x5 or 8x8
  final BingoCardTier tier;           // Bronze, Silver, Gold
  final List<List<BingoSquare>> grid; // 2D grid of squares
  final DateTime createdAt;           // When card was generated
  final String userId;                // Who owns this card
  bool isCompleted;                   // Has the game finished?
  bool hasWon;                        // Did the player win?
  String? winPattern;                 // What pattern won (if any)
  int pointsEarned;                   // Points from this card

  BingoCard({
    required this.id,
    required this.type,
    required this.tier,
    required this.grid,
    required this.createdAt,
    required this.userId,
    this.isCompleted = false,
    this.hasWon = false,
    this.winPattern,
    this.pointsEarned = 0,
  });

  // Get grid size based on card type
  int get gridSize {
    switch (type) {
      case BingoCardType.scratch3x3:
        return 3;
      case BingoCardType.small5x5:
        return 5;
      case BingoCardType.large8x8:
        return 8;
    }
  }

  // Get total number of squares
  int get totalSquares => gridSize * gridSize;

  // Get center square (free space for 5x5 cards only)
  BingoSquare? get centerSquare {
    if (type == BingoCardType.small5x5) {
      return grid[2][2]; // Center of 5x5 grid
    }
    return null; // 3x3 and 8x8 cards don't have free center
  }

  // Mark a square when symbol is called or revealed
  void markSquare(int row, int col, {bool isCallerMode = true}) {
    if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
      if (isCallerMode) {
        grid[row][col].isMarked = true;
      } else {
        // Scratch mode - reveal the symbol
        grid[row][col].isRevealed = true;
        grid[row][col].isMarked = true;
      }
    }
  }

  // Check if a symbol exists on the card and mark all instances
  List<List<int>> markSymbol(FoodSymbol symbol) {
    List<List<int>> markedPositions = [];

    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (grid[row][col].symbol == symbol && !grid[row][col].isMarked) {
          grid[row][col].isMarked = true;
          markedPositions.add([row, col]);
        }
      }
    }

    return markedPositions;
  }

  // Check for winning patterns
  bool checkForWin() {
    // Special logic for 3x3 scratch cards
    if (type == BingoCardType.scratch3x3) {
      return _checkScratchCardWin();
    }

    // Regular bingo logic for 5x5 and 8x8 cards
    // Check horizontal lines
    for (int row = 0; row < gridSize; row++) {
      if (grid[row].every((square) => square.isMarked)) {
        winPattern = 'Horizontal Line ${row + 1}';
        hasWon = true;
        return true;
      }
    }

    // Check vertical lines
    for (int col = 0; col < gridSize; col++) {
      bool isVerticalWin = true;
      for (int row = 0; row < gridSize; row++) {
        if (!grid[row][col].isMarked) {
          isVerticalWin = false;
          break;
        }
      }
      if (isVerticalWin) {
        winPattern = 'Vertical Line ${col + 1}';
        hasWon = true;
        return true;
      }
    }

    // Check diagonal (top-left to bottom-right)
    bool isDiagonal1Win = true;
    for (int i = 0; i < gridSize; i++) {
      if (!grid[i][i].isMarked) {
        isDiagonal1Win = false;
        break;
      }
    }
    if (isDiagonal1Win) {
      winPattern = 'Diagonal \\';
      hasWon = true;
      return true;
    }

    // Check diagonal (top-right to bottom-left)
    bool isDiagonal2Win = true;
    for (int i = 0; i < gridSize; i++) {
      if (!grid[i][gridSize - 1 - i].isMarked) {
        isDiagonal2Win = false;
        break;
      }
    }
    if (isDiagonal2Win) {
      winPattern = 'Diagonal /';
      hasWon = true;
      return true;
    }

    // Check special patterns for 5x5 cards
    if (type == BingoCardType.small5x5) {
      if (_checkSpecialPatterns()) {
        hasWon = true;
        return true;
      }
    }

    return false;
  }

  // Check for 3x3 scratch card wins (3 matching symbols)
  bool _checkScratchCardWin() {
    // Count revealed symbols
    Map<String, int> symbolCounts = {};

    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        final square = grid[row][col];
        if (square.isRevealed) {
          final symbolId = square.symbol.id;
          symbolCounts[symbolId] = (symbolCounts[symbolId] ?? 0) + 1;

          // Check if we have 3 matching symbols
          if (symbolCounts[symbolId]! >= 3) {
            winPattern = '3 Matching ${square.symbol.name}';
            hasWon = true;

            // Mark all matching symbols
            for (int r = 0; r < gridSize; r++) {
              for (int c = 0; c < gridSize; c++) {
                if (grid[r][c].symbol.id == symbolId) {
                  grid[r][c].isMarked = true;
                }
              }
            }

            return true;
          }
        }
      }
    }

    return false;
  }

  // Check special patterns (X, corners, etc.)
  bool _checkSpecialPatterns() {
    // X pattern (both diagonals)
    bool isXPattern = true;
    for (int i = 0; i < gridSize; i++) {
      if (!grid[i][i].isMarked || !grid[i][gridSize - 1 - i].isMarked) {
        isXPattern = false;
        break;
      }
    }
    if (isXPattern) {
      winPattern = 'X Pattern';
      return true;
    }

    // Four corners pattern
    if (grid[0][0].isMarked &&
        grid[0][gridSize - 1].isMarked &&
        grid[gridSize - 1][0].isMarked &&
        grid[gridSize - 1][gridSize - 1].isMarked) {
      winPattern = 'Four Corners';
      return true;
    }

    // Full card (blackout)
    bool isBlackout = true;
    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (!grid[row][col].isMarked) {
          isBlackout = false;
          break;
        }
      }
      if (!isBlackout) break;
    }
    if (isBlackout) {
      winPattern = 'Blackout';
      return true;
    }

    return false;
  }

  // Convert to JSON for Firebase storage
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type.name,
      'tier': tier.name,
      'grid': grid.map((row) => row.map((square) => square.toJson()).toList()).toList(),
      'createdAt': createdAt.toIso8601String(),
      'userId': userId,
      'isCompleted': isCompleted,
      'hasWon': hasWon,
      'winPattern': winPattern,
      'pointsEarned': pointsEarned,
    };
  }

  factory BingoCard.fromJson(Map<String, dynamic> json) {
    return BingoCard(
      id: json['id'] ?? '',
      type: BingoCardType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => BingoCardType.small5x5,
      ),
      tier: BingoCardTier.values.firstWhere(
        (e) => e.name == json['tier'],
        orElse: () => BingoCardTier.bronze,
      ),
      grid: (json['grid'] as List)
          .map((row) => (row as List)
              .map((square) => BingoSquare.fromJson(square))
              .toList())
          .toList(),
      createdAt: DateTime.parse(json['createdAt']),
      userId: json['userId'] ?? '',
      isCompleted: json['isCompleted'] ?? false,
      hasWon: json['hasWon'] ?? false,
      winPattern: json['winPattern'],
      pointsEarned: json['pointsEarned'] ?? 0,
    );
  }

  // Generate a new random bingo card
  factory BingoCard.generate({
    required String userId,
    required BingoCardType type,
    required BingoCardTier tier,
  }) {
    if (type == BingoCardType.scratch3x3) {
      return _generateScratchCard(userId, tier);
    }

    final gridSize = type == BingoCardType.small5x5 ? 5 : 8;
    final totalSquares = gridSize * gridSize;

    // Get random symbols (ensuring no duplicates)
    final symbols = FoodSymbols.getRandomSymbols(totalSquares);

    // Create the grid
    List<List<BingoSquare>> grid = [];
    int symbolIndex = 0;

    for (int row = 0; row < gridSize; row++) {
      List<BingoSquare> rowSquares = [];
      for (int col = 0; col < gridSize; col++) {
        // For 5x5 cards, center square is free (automatically marked)
        bool isFreeSpace = (type == BingoCardType.small5x5 && row == 2 && col == 2);

        rowSquares.add(BingoSquare(
          symbol: symbols[symbolIndex],
          isMarked: isFreeSpace,
          isRevealed: false,
        ));
        symbolIndex++;
      }
      grid.add(rowSquares);
    }

    return BingoCard(
      id: 'card_${DateTime.now().millisecondsSinceEpoch}_${userId.substring(0, 8)}',
      type: type,
      tier: tier,
      grid: grid,
      createdAt: DateTime.now(),
      userId: userId,
    );
  }

  // Generate a 3x3 scratch card with 25% win rate
  static BingoCard _generateScratchCard(String userId, BingoCardTier tier) {
    final random = Random();
    final shouldWin = random.nextDouble() < 0.25; // 25% win rate

    List<FoodSymbol> symbols;

    if (shouldWin) {
      // Create a winning card (at least 3 matching symbols)
      final winningSymbol = FoodSymbols.getRandomSymbols(1).first;
      final otherSymbols = FoodSymbols.getRandomSymbols(6);

      // Ensure we have exactly 3 of the winning symbol and 6 others
      symbols = [
        winningSymbol, winningSymbol, winningSymbol, // 3 matching for win
        ...otherSymbols.take(6), // 6 different symbols
      ];

      // Shuffle the positions
      symbols.shuffle();
    } else {
      // Create a losing card (no 3 matching symbols)
      symbols = _generateLosingSymbols();
    }

    // Create the 3x3 grid
    List<List<BingoSquare>> grid = [];
    int symbolIndex = 0;

    for (int row = 0; row < 3; row++) {
      List<BingoSquare> rowSquares = [];
      for (int col = 0; col < 3; col++) {
        rowSquares.add(BingoSquare(
          symbol: symbols[symbolIndex],
          isMarked: false,
          isRevealed: false,
        ));
        symbolIndex++;
      }
      grid.add(rowSquares);
    }

    return BingoCard(
      id: 'scratch_${DateTime.now().millisecondsSinceEpoch}_${userId.substring(0, 8)}',
      type: BingoCardType.scratch3x3,
      tier: tier,
      grid: grid,
      createdAt: DateTime.now(),
      userId: userId,
    );
  }

  // Generate symbols for a losing scratch card (no 3 matches)
  static List<FoodSymbol> _generateLosingSymbols() {
    final allSymbols = FoodSymbols.allSymbols;
    final random = Random();
    List<FoodSymbol> symbols = [];

    // Strategy: Pick symbols ensuring no symbol appears 3+ times
    Map<String, int> symbolCounts = {};

    while (symbols.length < 9) {
      final randomSymbol = allSymbols[random.nextInt(allSymbols.length)];
      final currentCount = symbolCounts[randomSymbol.id] ?? 0;

      // Only add if it won't create 3 matches
      if (currentCount < 2) {
        symbols.add(randomSymbol);
        symbolCounts[randomSymbol.id] = currentCount + 1;
      }
    }

    return symbols;
  }

  // Get progress percentage (how many squares are marked)
  double get progress {
    int markedCount = 0;
    int totalCount = totalSquares;

    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        if (grid[row][col].isMarked) {
          markedCount++;
        }
      }
    }

    return markedCount / totalCount;
  }

  // Get all symbols on this card
  List<FoodSymbol> get allSymbols {
    List<FoodSymbol> symbols = [];
    for (int row = 0; row < gridSize; row++) {
      for (int col = 0; col < gridSize; col++) {
        symbols.add(grid[row][col].symbol);
      }
    }
    return symbols;
  }

  @override
  String toString() {
    return 'BingoCard(id: $id, type: $type, tier: $tier, hasWon: $hasWon)';
  }
}