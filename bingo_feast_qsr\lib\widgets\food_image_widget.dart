import 'package:flutter/material.dart';
import '../models/food_symbol.dart';

/// A widget that displays food images with emoji fallbacks
/// This makes it easy to update to real images later while having
/// beautiful placeholders now
class FoodImageWidget extends StatelessWidget {
  final FoodSymbol symbol;
  final double size;
  final bool isMarked;
  final bool isRevealed;
  final bool isCenterFree;

  const FoodImageWidget({
    super.key,
    required this.symbol,
    this.size = 32,
    this.isMarked = false,
    this.isRevealed = true,
    this.isCenterFree = false,
  });

  @override
  Widget build(BuildContext context) {
    // Try to load the actual image first, fallback to emoji
    return FutureBuilder<bool>(
      future: _imageExists(),
      builder: (context, snapshot) {
        final imageExists = snapshot.data ?? false;
        
        if (imageExists) {
          return _buildImageWidget();
        } else {
          return _buildEmojiWidget();
        }
      },
    );
  }

  /// Check if the image file exists
  Future<bool> _imageExists() async {
    try {
      // For now, we'll use emoji fallbacks since we don't have actual images
      // In the future, this can check if the image asset exists
      return false; // Always use emoji for now
    } catch (e) {
      return false;
    }
  }

  /// Build widget using actual image
  Widget _buildImageWidget() {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          if (isMarked) BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(8),
        child: Image.asset(
          symbol.imagePath,
          width: size,
          height: size,
          fit: BoxFit.cover,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to emoji if image fails to load
            return _buildEmojiWidget();
          },
        ),
      ),
    );
  }

  /// Build widget using emoji placeholder
  Widget _buildEmojiWidget() {
    final emoji = _getFoodEmoji(symbol.id);
    final fontSize = size * 0.6; // Scale emoji to fit nicely
    
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.white,
            Colors.grey.shade50,
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isMarked ? Colors.orange.shade400 : Colors.grey.shade300,
          width: isMarked ? 2 : 1,
        ),
        boxShadow: [
          if (isMarked) BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Center(
        child: Text(
          emoji,
          style: TextStyle(
            fontSize: fontSize,
            height: 1.0,
          ),
        ),
      ),
    );
  }

  /// Get emoji for each food symbol
  String _getFoodEmoji(String symbolId) {
    switch (symbolId) {
      // Main items
      case 'burger_classic':
        return '🍔';
      case 'chicken_crispy':
        return '🍗';
      case 'chicken_wings':
        return '🍖';
      case 'fish_sandwich':
        return '🐟';
      case 'hot_dog':
        return '🌭';
      case 'pizza_slice':
        return '🍕';
      case 'taco':
        return '🌮';
      case 'sandwich':
        return '🥪';
      case 'salad':
        return '🥗';
      
      // Side items
      case 'fries_regular':
        return '🍟';
      case 'onion_rings':
        return '🧅';
      case 'mozzarella_sticks':
        return '🧀';
      case 'coleslaw':
        return '🥬';
      case 'mac_cheese':
        return '🧀';
      case 'baked_beans':
        return '🫘';
      case 'corn':
        return '🌽';
      case 'potato_wedges':
        return '🥔';
      
      // Drinks
      case 'soda_cola':
        return '🥤';
      case 'milkshake':
        return '🥛';
      case 'lemonade':
        return '🍋';
      case 'coffee':
        return '☕';
      case 'orange_juice':
        return '🍊';
      case 'water':
        return '💧';
      case 'tea':
        return '🧊';
      
      // Desserts
      case 'ice_cream':
        return '🍦';
      case 'apple_pie':
        return '🥧';
      case 'chocolate_cake':
        return '🍰';
      case 'cookies':
        return '🍪';
      case 'donut':
        return '🍩';
      case 'brownie':
        return '🟫';
      case 'muffin':
        return '🧁';
      case 'cheesecake':
        return '🍰';
      case 'pudding':
        return '🍮';
      
      // Default fallback
      default:
        return '🍽️';
    }
  }
}

/// A specialized widget for the FREE space
class FreeSpaceWidget extends StatelessWidget {
  final double size;
  final bool isMarked;

  const FreeSpaceWidget({
    super.key,
    this.size = 32,
    this.isMarked = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.orange.shade100,
            Colors.orange.shade200,
          ],
        ),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: Colors.orange.shade400,
          width: 2,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.orange.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.star,
            color: Colors.orange.shade700,
            size: size * 0.4,
          ),
          Text(
            'FREE',
            style: TextStyle(
              fontSize: size * 0.15,
              fontWeight: FontWeight.bold,
              color: Colors.orange.shade700,
            ),
          ),
        ],
      ),
    );
  }
}
