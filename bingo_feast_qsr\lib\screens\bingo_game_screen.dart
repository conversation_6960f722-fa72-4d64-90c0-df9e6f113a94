// 🎯 Bingo Game Screen
// This is the main game screen where players play bingo with Chef <PERSON>

import 'dart:async';
import 'dart:math';
import 'package:flutter/material.dart';
// import 'package:provider/provider.dart';
import '../models/bingo_card.dart';
import '../models/game_state.dart';
import '../models/food_symbol.dart';
import '../services/virtual_caller_service.dart';
import '../services/audio_service.dart';
// import '../widgets/bingo_card_widget.dart';
// import '../widgets/chef_bob_widget.dart';
// import '../widgets/game_controls_widget.dart';
// import '../widgets/game_progress_widget.dart';
import '../widgets/food_image_widget.dart';

// Reusable hover animation widget for game screen
class GameHoverWidget extends StatefulWidget {
  final Widget child;
  final double hoverScale;
  final Duration duration;
  final VoidCallback? onTap;

  const GameHoverWidget({
    super.key,
    required this.child,
    this.hoverScale = 1.05,
    this.duration = const Duration(milliseconds: 200),
    this.onTap,
  });

  @override
  State<GameHoverWidget> createState() => _GameHoverWidgetState();
}

class _GameHoverWidgetState extends State<GameHoverWidget> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      child: GestureDetector(
        onTap: widget.onTap,
        child: AnimatedScale(
          scale: _isHovered ? widget.hoverScale : 1.0,
          duration: widget.duration,
          curve: Curves.easeInOut,
          child: widget.child,
        ),
      ),
    );
  }
}

// Beautiful win animation widget
class WinAnimationWidget extends StatefulWidget {
  final VoidCallback onComplete;

  const WinAnimationWidget({
    super.key,
    required this.onComplete,
  });

  @override
  State<WinAnimationWidget> createState() => _WinAnimationWidgetState();
}

class _WinAnimationWidgetState extends State<WinAnimationWidget>
    with TickerProviderStateMixin {
  late AnimationController _controller;
  late AnimationController _particleController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _rotationAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.elasticOut,
    ));

    _rotationAnimation = Tween<double>(
      begin: 0.0,
      end: 2.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeIn,
    ));

    // Start animations
    _controller.forward();
    _particleController.repeat();

    // Complete after animation
    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(const Duration(milliseconds: 500), () {
          widget.onComplete();
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Stack(
        children: [
          // Particle effects
          ...List.generate(20, (index) => _buildParticle(index)),

          // Main win content
          Center(
            child: AnimatedBuilder(
              animation: _controller,
              builder: (context, child) {
                return Transform.scale(
                  scale: _scaleAnimation.value,
                  child: Transform.rotate(
                    angle: _rotationAnimation.value * 0.1,
                    child: Opacity(
                      opacity: _fadeAnimation.value,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          // Trophy with glow effect
                          Container(
                            padding: const EdgeInsets.all(32),
                            decoration: BoxDecoration(
                              gradient: RadialGradient(
                                colors: [
                                  Colors.amber.shade300,
                                  Colors.orange.shade400,
                                  Colors.red.shade400,
                                ],
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.amber.withValues(alpha: 0.6),
                                  blurRadius: 30,
                                  spreadRadius: 10,
                                ),
                              ],
                            ),
                            child: const Icon(
                              Icons.emoji_events,
                              size: 80,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(height: 32),

                          // Win text with shimmer effect
                          ShaderMask(
                            shaderCallback: (bounds) => LinearGradient(
                              colors: [
                                Colors.amber.shade300,
                                Colors.orange.shade400,
                                Colors.red.shade400,
                                Colors.amber.shade300,
                              ],
                              stops: const [0.0, 0.3, 0.7, 1.0],
                            ).createShader(bounds),
                            child: Text(
                              'WINNER!',
                              style: TextStyle(
                                fontSize: 48,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                shadows: [
                                  Shadow(
                                    color: Colors.black.withValues(alpha: 0.5),
                                    offset: const Offset(2, 2),
                                    blurRadius: 4,
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildParticle(int index) {
    final random = Random(index);
    final startX = random.nextDouble() * MediaQuery.of(context).size.width;
    final startY = MediaQuery.of(context).size.height;
    final endY = -100.0;
    final color = [
      Colors.amber,
      Colors.orange,
      Colors.red,
      Colors.yellow,
      Colors.pink,
    ][random.nextInt(5)];

    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        final progress = _particleController.value;
        final currentY = startY + (endY - startY) * progress;
        final opacity = 1.0 - progress;

        return Positioned(
          left: startX,
          top: currentY,
          child: Opacity(
            opacity: opacity,
            child: Transform.rotate(
              angle: progress * 4 * pi,
              child: Icon(
                Icons.star,
                color: color,
                size: 20 + random.nextDouble() * 10,
              ),
            ),
          ),
        );
      },
    );
  }
}

class BingoGameScreen extends StatefulWidget {
  final BingoCard bingoCard;
  final GameMode gameMode;

  const BingoGameScreen({
    super.key,
    required this.bingoCard,
    required this.gameMode,
  });

  @override
  State<BingoGameScreen> createState() => _BingoGameScreenState();
}

class _BingoGameScreenState extends State<BingoGameScreen>
    with TickerProviderStateMixin {
  late GameState _gameState;
  late VirtualCallerService _callerService;
  late AudioService _audioService;

  // Animation controllers
  late AnimationController _cardAnimationController;
  late AnimationController _chefBobAnimationController;
  late Animation<double> _cardScaleAnimation;
  late Animation<Offset> _chefBobSlideAnimation;

  // UI state
  bool _isGameStarted = false;
  bool _showInstructions = true;
  String _currentMessage = '';
  FoodSymbol? _lastCalledSymbol;

  @override
  void initState() {
    super.initState();
    _initializeGame();
    _setupAnimations();
    _setupServices();
  }

  void _initializeGame() {
    // Create game state
    _gameState = GameState(
      gameId: 'game_${DateTime.now().millisecondsSinceEpoch}',
      card: widget.bingoCard,
      mode: widget.gameMode,
    );

    // Initialize services
    _callerService = VirtualCallerService();
    _audioService = AudioService();
  }

  void _setupAnimations() {
    // Card animation for when symbols are called
    _cardAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _cardScaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.05,
    ).animate(CurvedAnimation(
      parent: _cardAnimationController,
      curve: Curves.elasticOut,
    ));

    // Chef Bob slide animation
    _chefBobAnimationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _chefBobSlideAnimation = Tween<Offset>(
      begin: const Offset(-1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _chefBobAnimationController,
      curve: Curves.bounceOut,
    ));
  }

  void _setupServices() {
    // Initialize services
    _callerService.initialize();
    _audioService.initialize();

    // Set up callbacks for Chef Bob state changes
    _callerService.setOnChefBobStateChanged((chefBob) {
      setState(() {
        _currentMessage = chefBob.currentMessage;
      });
    });

    // Set up callbacks for symbol calls
    _callerService.setOnSymbolCalled((symbol) {
      setState(() {
        _lastCalledSymbol = symbol;
      });
      _animateCardForSymbol(symbol);
    });

    // Set up callbacks for game events
    _callerService.setOnGameEvent((event, data) {
      _handleGameEvent(event, data);
    });

    // Show Chef Bob with animation
    Future.delayed(const Duration(milliseconds: 500), () {
      _chefBobAnimationController.forward();
    });
  }

  // Handle game events from the virtual caller
  void _handleGameEvent(String event, Map<String, dynamic> data) {
    switch (event) {
      case 'calling_started':
        setState(() {
          _isGameStarted = true;
          _showInstructions = false;
        });
        break;
      case 'symbol_called':
        // Symbol was called, check if player has it
        _checkForWin();
        break;
      case 'win_celebrated':
        _showWinDialog();
        break;
      case 'player_consoled':
        _showConsolationDialog();
        break;
    }
  }

  // Animate the card when a symbol is called
  void _animateCardForSymbol(FoodSymbol symbol) {
    _cardAnimationController.forward().then((_) {
      _cardAnimationController.reverse();
    });
  }

  // Check if player won after each symbol call or scratch
  void _checkForWin() {
    if (_gameState.card.checkForWin()) {
      _gameState.hasWon = true;
      _gameState.status = GameStatus.completed;

      // Calculate points for winning
      if (_gameState.card.type == BingoCardType.scratch3x3) {
        _gameState.pointsEarned = 100; // Higher reward for scratch card wins
      } else {
        _gameState.pointsEarned = widget.gameMode == GameMode.scratch ? 50 : 25;
      }
      _gameState.card.pointsEarned = _gameState.pointsEarned;

      if (widget.gameMode == GameMode.caller) {
        _callerService.celebrateWin();
      } else {
        // Scratch mode - show immediate win feedback
        _callerService.celebrateWin();
      }

      _audioService.playWin();
      _showWinDialog();
    } else if (widget.gameMode == GameMode.scratch) {
      // In scratch mode, check if all squares are revealed without a win
      bool allRevealed = true;
      for (int row = 0; row < _gameState.card.gridSize; row++) {
        for (int col = 0; col < _gameState.card.gridSize; col++) {
          final square = _gameState.card.grid[row][col];
          final isCenterFree = _gameState.card.type == BingoCardType.small5x5 &&
                              row == 2 && col == 2;
          if (!square.isRevealed && !isCenterFree) {
            allRevealed = false;
            break;
          }
        }
        if (!allRevealed) break;
      }

      if (allRevealed && !_gameState.hasWon) {
        // All squares revealed but no win - give consolation points
        _gameState.status = GameStatus.completed;
        if (_gameState.card.type == BingoCardType.scratch3x3) {
          _gameState.pointsEarned = 10; // Consolation for 3x3 scratch cards
        } else {
          _gameState.pointsEarned = _gameState.card.type == BingoCardType.small5x5 ? 5 : 10;
        }
        _gameState.card.pointsEarned = _gameState.pointsEarned;
        _callerService.consolePlayer();
        _showConsolationDialog();
      }
    }
  }

  // Start the game
  void _startGame() async {
    print('🎮 Starting game in ${widget.gameMode} mode');

    setState(() {
      _isGameStarted = true;
      _showInstructions = false;
    });

    // Check if this is the user's first time playing
    final isFirstTime = await _isFirstTimePlayer();
    if (isFirstTime) {
      print('🎉 First time player - setting up guaranteed win!');
      _setupGuaranteedWin();
    }

    _gameState.startGame();
    print('🎯 Game state started: ${_gameState.status}');

    if (widget.gameMode == GameMode.caller) {
      print('📞 Starting caller mode...');
      await _callerService.greetPlayer();

      // Wait a moment for greeting, then start calling
      await Future.delayed(const Duration(seconds: 3));
      _startCallerMode();
    } else {
      // Scratch mode - just greet the player and let them start scratching
      await _callerService.greetPlayer();
      if (isFirstTime) {
        _setupScratchGuaranteedWin();
      }
    }
  }

  // Check if this is the user's first time playing
  Future<bool> _isFirstTimePlayer() async {
    // In a real app, this would check a database or shared preferences
    // For demo purposes, we'll always return true for the first game
    return true; // Always guarantee first win for demo
  }

  // Setup guaranteed win for caller mode
  void _setupGuaranteedWin() {
    print('🎯 Setting up guaranteed win for caller mode');
    // We'll modify the calling sequence to ensure a win
    // This will be done in the _startCallerMode method
  }

  // Setup guaranteed win for scratch mode
  void _setupScratchGuaranteedWin() {
    print('🎯 Setting up guaranteed win for scratch mode');
    // For 3x3 scratch cards, we'll ensure at least 3 matching symbols
    if (_gameState.card.type == BingoCardType.scratch3x3) {
      final grid = _gameState.card.grid;
      final firstSymbol = grid[0][0].symbol;

      // Create new squares with the same symbol for guaranteed win
      grid[0][1] = BingoSquare(
        symbol: firstSymbol,
        isMarked: false,
        isRevealed: false,
      );
      grid[0][2] = BingoSquare(
        symbol: firstSymbol,
        isMarked: false,
        isRevealed: false,
      );

      print('🎉 Guaranteed 3 matching symbols: ${firstSymbol.name}');
    }
  }

  // Start caller mode (Chef Bob calls symbols)
  void _startCallerMode() {
    print('🎲 Starting caller mode...');
    print('🎯 Game status: ${_gameState.status}');
    print('🎯 Game mode: ${_gameState.mode}');

    // Generate calling sequence first
    _gameState.generateCallingSequence();
    print('📋 Generated ${_gameState.callingSequence.length} symbols to call');
    print('🎯 Max calls: ${_gameState.maxCalls}');
    print('🎯 Current call index: ${_gameState.currentCallIndex}');

    // Setup guaranteed win for first-time players
    _setupCallerGuaranteedWin();

    // Check if we have symbols to call
    if (_gameState.callingSequence.isEmpty) {
      print('❌ ERROR: No symbols in calling sequence!');
      return;
    }

    // Manual calling mode - user taps Chef Bob to call foods
    // No automatic calling
    print('👨‍🍳 Manual calling mode activated - tap Chef Bob to call foods!');
  }

  // Setup guaranteed win for caller mode
  void _setupCallerGuaranteedWin() {
    print('🎯 Setting up guaranteed win for caller mode');

    // For 5x5 cards, ensure the center row can win
    if (_gameState.card.type == BingoCardType.small5x5) {
      final grid = _gameState.card.grid;
      final centerRowSymbols = <FoodSymbol>[];

      // Collect symbols from center row (row 2)
      for (int col = 0; col < 5; col++) {
        if (col != 2) { // Skip the FREE space
          centerRowSymbols.add(grid[2][col].symbol);
        }
      }

      // Ensure these symbols are called early in the sequence
      final callingSequence = _gameState.callingSequence;
      for (int i = 0; i < centerRowSymbols.length && i < callingSequence.length; i++) {
        // Move center row symbols to the beginning of calling sequence
        final symbolIndex = callingSequence.indexWhere((s) => s.name == centerRowSymbols[i].name);
        if (symbolIndex > i) {
          final symbol = callingSequence.removeAt(symbolIndex);
          callingSequence.insert(i, symbol);
        }
      }

      print('🎉 Guaranteed center row win setup complete');
    }
  }

  // Make the next call in the sequence
  void _makeNextCall() {
    print('🎯 _makeNextCall called - Index: ${_gameState.currentCallIndex}, Sequence length: ${_gameState.callingSequence.length}');

    if (_gameState.currentCallIndex >= _gameState.callingSequence.length) {
      print('❌ No more symbols to call - reached end of sequence');
      _endGameWithoutWin();
      return;
    }

    if (_gameState.currentCallIndex >= _gameState.maxCalls) {
      print('❌ No more symbols to call - reached max calls');
      _endGameWithoutWin();
      return;
    }

    // Get next symbol to call
    final symbol = _gameState.callingSequence[_gameState.currentCallIndex];
    print('📢 Calling symbol ${_gameState.currentCallIndex + 1}/${_gameState.maxCalls}: ${symbol.name}');

    // Update last called symbol for UI
    setState(() {
      _lastCalledSymbol = symbol;
    });

    // Make Chef Bob call it
    _callerService.callSymbol(symbol);

    // Update game state
    _gameState.currentSymbol = symbol;

    // Mark matching symbols on card
    final markedPositions = _gameState.card.markSymbol(symbol);
    print('🎯 Marked ${markedPositions.length} squares for ${symbol.name}');

    // Increment after processing
    _gameState.currentCallIndex++;
    print('🎯 Incremented call index to ${_gameState.currentCallIndex}');

    // Check for win
    if (_gameState.card.checkForWin()) {
      print('🎉 Player won with pattern: ${_gameState.card.winPattern}');
      _gameState.hasWon = true;
      _callerService.celebrateWin();
      _showWinDialog();
    }

    setState(() {});
  }

  // End game without win (max calls reached)
  void _endGameWithoutWin() {
    print('🏁 Game ended without win - max calls reached');

    setState(() {
      _gameState.status = GameStatus.completed;
      _gameState.hasWon = false;
      _gameState.pointsEarned = 5; // Consolation points
    });

    // Show consolation dialog
    _showConsolationDialog();
  }

  // Show win dialog with beautiful animations
  void _showWinDialog() {
    // Play win animation first
    _showWinAnimation();

    // Show dialog after animation
    Future.delayed(const Duration(milliseconds: 1500), () {
      if (mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (context) => _buildWinDialog(),
        );
      }
    });
  }

  // Beautiful win animation overlay
  void _showWinAnimation() {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black.withValues(alpha: 0.8),
      builder: (context) => WinAnimationWidget(
        onComplete: () => Navigator.of(context).pop(),
      ),
    );
  }

  // Build the win dialog
  Widget _buildWinDialog() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: AlertDialog(
              backgroundColor: Colors.transparent,
              content: Container(
                padding: const EdgeInsets.all(32),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.amber.shade100,
                      Colors.orange.shade100,
                      Colors.red.shade100,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.amber.withValues(alpha: 0.4),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Trophy Icon
                    Container(
                      padding: const EdgeInsets.all(20),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.amber.shade400, Colors.orange.shade500],
                        ),
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.emoji_events,
                        size: 48,
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Win Title
                    ShaderMask(
                      shaderCallback: (bounds) => LinearGradient(
                        colors: [Colors.amber.shade700, Colors.orange.shade600],
                      ).createShader(bounds),
                      child: Text(
                        '🎉 BINGO! 🎉',
                        style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Win Details
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.9),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Column(
                        children: [
                          Text(
                            'Congratulations!',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Colors.orange.shade800,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'You won with: ${_gameState.card.winPattern}',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: Colors.orange.shade700,
                            ),
                            textAlign: TextAlign.center,
                          ),
                          if (_gameState.reward != null) ...[
                            const SizedBox(height: 12),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [Colors.green.shade100, Colors.green.shade200],
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                '🎁 Reward: ${_gameState.reward!.description}',
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.green.shade800,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Play Again Button
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade400, Colors.purple.shade400],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.blue.withValues(alpha: 0.4),
                            blurRadius: 10,
                            offset: const Offset(0, 5),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            Navigator.of(context).pop();
                            Navigator.of(context).pop(); // Return to previous screen
                          },
                          borderRadius: BorderRadius.circular(16),
                          child: Padding(
                            padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(Icons.play_arrow, color: Colors.white),
                                const SizedBox(width: 8),
                                Text(
                                  'Play Again',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Show consolation dialog
  void _showConsolationDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('Good Game!'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text('Better luck next time!'),
            const SizedBox(height: 16),
            Text('You earned ${_gameState.pointsEarned} points for playing!'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // Return to previous screen
            },
            child: const Text('Play Again'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isTablet = screenSize.width > 600;
    final isMobile = screenSize.width < 600;

    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: _getGameModeGradient(),
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Enhanced Header
              _buildEnhancedHeader(context),

              // Game Content
              Expanded(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(isMobile ? 12 : 16),
                  child: Column(
                    children: [
                      if (!_isGameStarted) _buildInstructionsCard(),
                      if (_isGameStarted) ...[
                        // Only show game status for caller mode, not scratch mode
                        if (widget.gameMode == GameMode.caller) ...[
                          _buildGameStatusCard(),
                          SizedBox(height: isMobile ? 12 : 20),
                        ],
                        _buildEnhancedBingoCard(),
                        SizedBox(height: isMobile ? 12 : 20),
                        // No caller section for scratch mode
                      ],
                    ],
                  ),
                ),
              ),

              // No game controls needed - removed pause/quit buttons
            ],
          ),
        ),
      ),
    );
  }

  List<Color> _getGameModeGradient() {
    switch (widget.bingoCard.type) {
      case BingoCardType.small5x5:
        return [Colors.blue.shade300, Colors.blue.shade500, Colors.indigo.shade400];
      case BingoCardType.scratch3x3:
        return [Colors.purple.shade300, Colors.purple.shade500, Colors.pink.shade400];
      case BingoCardType.large8x8:
        return [Colors.red.shade300, Colors.red.shade500, Colors.orange.shade400];
    }
  }

  Widget _buildEnhancedHeader(BuildContext context) {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, -50 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.25),
                    Colors.white.withValues(alpha: 0.15),
                  ],
                ),
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(30),
                  bottomRight: Radius.circular(30),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Row(
                children: [
                  // Back Button
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Colors.white.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: IconButton(
                      icon: const Icon(Icons.arrow_back, color: Colors.white),
                      onPressed: () => Navigator.of(context).pop(),
                    ),
                  ),

                  const SizedBox(width: 16),

                  // Game Title
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.gameMode == GameMode.caller ? '🎯 Bingo Game' : '🎫 Scratch Card',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            shadows: [
                              Shadow(
                                color: Colors.black.withValues(alpha: 0.3),
                                offset: const Offset(0, 1),
                                blurRadius: 2,
                              ),
                            ],
                          ),
                        ),
                        Text(
                          '${_gameState.card.gridSize}x${_gameState.card.gridSize} ${_gameState.card.type.name}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white.withValues(alpha: 0.8),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Points Display
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.amber.shade400, Colors.orange.shade500],
                      ),
                      borderRadius: BorderRadius.circular(20),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withValues(alpha: 0.4),
                          blurRadius: 8,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(Icons.stars, color: Colors.white, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '500', // Demo points
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildInstructionsCard() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white,
                    Colors.blue.shade50,
                  ],
                ),
                borderRadius: BorderRadius.circular(20),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 15,
                    offset: const Offset(0, 8),
                  ),
                ],
              ),
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Colors.blue.shade400, Colors.purple.shade400],
                      ),
                      shape: BoxShape.circle,
                    ),
                    child: const Icon(
                      Icons.help_outline,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'How to Play',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: Colors.blue.shade800,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Text(
                    widget.gameMode == GameMode.caller
                      ? 'Chef Bob will call out food symbols. Mark matching squares on your card to win!'
                      : _gameState.card.type == BingoCardType.scratch3x3
                        ? 'Scratch to reveal symbols. Find 3 matching symbols to win instantly!'
                        : 'Scratch squares to reveal symbols. Match lines, diagonals, or patterns to win!',
                    style: Theme.of(context).textTheme.bodyLarge,
                    textAlign: TextAlign.center,
                  ),
                  if (widget.gameMode == GameMode.scratch) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        _gameState.card.type == BingoCardType.scratch3x3
                          ? '💡 Tip: You need 3 matching food symbols anywhere on the card!'
                          : '💡 Tip: Swipe your finger across squares to scratch them off!',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          fontStyle: FontStyle.italic,
                          color: Colors.amber.shade800,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ],
                  const SizedBox(height: 20),
                  _buildEnhancedStartButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildEnhancedStartButton() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 1000),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * value),
          child: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [Colors.green.shade400, Colors.green.shade600],
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.green.withValues(alpha: 0.4),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _startGame,
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      const Icon(Icons.play_arrow, color: Colors.white, size: 28),
                      const SizedBox(width: 8),
                      Text(
                        'Start Game',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildGameStatusCard() {
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 400),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Container(
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.white.withValues(alpha: 0.9),
                    Colors.white.withValues(alpha: 0.7),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Progress Bar
                  Row(
                    children: [
                      Icon(Icons.timer, color: Colors.blue.shade600),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Progress: ${(_gameState.progress * 100).toInt()}%',
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                            const SizedBox(height: 4),
                            LinearProgressIndicator(
                              value: _gameState.progress,
                              backgroundColor: Colors.grey.shade300,
                              valueColor: AlwaysStoppedAnimation<Color>(Colors.blue.shade600),
                              minHeight: 6,
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  // Chef Bob Caller (for caller mode)
                  if (widget.gameMode == GameMode.caller) ...[
                    const SizedBox(height: 16),
                    _buildChefBobCaller(),
                  ],

                  // Last Called Symbol
                  if (_lastCalledSymbol != null) ...[
                    const SizedBox(height: 16),
                    TweenAnimationBuilder<double>(
                      duration: const Duration(milliseconds: 500),
                      tween: Tween(begin: 0.0, end: 1.0),
                      builder: (context, value, child) {
                        return Transform.scale(
                          scale: 0.9 + (0.1 * value),
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Colors.orange.shade200, Colors.orange.shade300],
                              ),
                              borderRadius: BorderRadius.circular(16),
                              border: Border.all(color: Colors.orange.shade400, width: 2),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.orange.withValues(alpha: 0.4),
                                  blurRadius: 10,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  padding: const EdgeInsets.all(8),
                                  decoration: BoxDecoration(
                                    color: Colors.orange.shade500,
                                    shape: BoxShape.circle,
                                  ),
                                  child: FoodImageWidget(
                                    symbol: _lastCalledSymbol!,
                                    size: 20,
                                    isMarked: false,
                                    isRevealed: true,
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Column(
                                  children: [
                                    Text(
                                      'Last Called',
                                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                        color: Colors.orange.shade700,
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    Text(
                                      _lastCalledSymbol!.name,
                                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                        fontWeight: FontWeight.bold,
                                        color: Colors.orange.shade800,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildChefBobCaller() {
    return GestureDetector(
      onTap: () {
        // Call next food when Chef Bob is tapped
        print('🎯 Chef Bob tapped! Status: ${_gameState.status}, CallIndex: ${_gameState.currentCallIndex}, MaxCalls: ${_gameState.maxCalls}');

        if (_gameState.status != GameStatus.playing) {
          print('❌ Game not playing, status: ${_gameState.status}');
          return;
        }

        if (_gameState.currentCallIndex >= _gameState.maxCalls) {
          print('❌ Max calls reached! ${_gameState.currentCallIndex}/${_gameState.maxCalls}');
          _endGameWithoutWin();
          return;
        }

        if (_gameState.currentCallIndex >= _gameState.callingSequence.length) {
          print('❌ No more symbols in sequence!');
          _endGameWithoutWin();
          return;
        }

        print('✅ Making next call...');
        _makeNextCall();
      },
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Colors.orange.shade300, Colors.orange.shade500],
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(color: Colors.orange.shade600, width: 2),
          boxShadow: [
            BoxShadow(
              color: Colors.orange.withValues(alpha: 0.4),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Chef Bob Character
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [Colors.orange.shade200, Colors.orange.shade400],
                ),
                borderRadius: BorderRadius.circular(30),
                border: Border.all(color: Colors.white, width: 2),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Chef Hat
                  Container(
                    width: 20,
                    height: 15,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: const Icon(
                      Icons.restaurant_menu,
                      color: Colors.orange,
                      size: 12,
                    ),
                  ),
                  const SizedBox(height: 2),
                  // Chef Face
                  const Icon(
                    Icons.person,
                    color: Colors.white,
                    size: 20,
                  ),
                ],
              ),
            ),
            const SizedBox(width: 16),
            // Instructions
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Chef Bob',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Tap me to call the next food!',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Colors.white.withValues(alpha: 0.9),
                    ),
                  ),
                ],
              ),
            ),
            // Call indicator
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                '${_gameState.currentCallIndex + 1}/${_gameState.maxCalls}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedBingoCard() {
    final screenSize = MediaQuery.of(context).size;
    final isMobile = screenSize.width < 600;
    final cardPadding = isMobile ? 12.0 : 20.0;
    final gridSpacing = isMobile ? 4.0 : 6.0;

    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 800),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.9 + (0.1 * value),
          child: Opacity(
            opacity: value,
            child: GameHoverWidget(
              hoverScale: 1.02,
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: isMobile ? screenSize.width - 24 : 500,
                ),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.white,
                      Colors.grey.shade50,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.15),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                    BoxShadow(
                      color: Colors.blue.withValues(alpha: 0.1),
                      blurRadius: 40,
                      offset: const Offset(0, 20),
                    ),
                  ],
                ),
                child: Padding(
                  padding: EdgeInsets.all(cardPadding),
                  child: Column(
                    children: [
                      // Card Header
                      GameHoverWidget(
                        hoverScale: 1.05,
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            vertical: isMobile ? 8 : 12,
                            horizontal: isMobile ? 12 : 16,
                          ),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.blue.shade400, Colors.purple.shade400],
                            ),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.grid_view, color: Colors.white),
                              const SizedBox(width: 8),
                              Flexible(
                                child: Text(
                                  '${_gameState.card.gridSize}x${_gameState.card.gridSize} ${widget.gameMode == GameMode.caller ? 'Bingo' : 'Scratch'} Card',
                                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: isMobile ? 14 : 16,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: isMobile ? 12 : 16),

                      // Bingo Grid
                      AspectRatio(
                        aspectRatio: 1.0, // Keep grid square
                        child: GridView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: _gameState.card.gridSize,
                            crossAxisSpacing: gridSpacing,
                            mainAxisSpacing: gridSpacing,
                          ),
                          itemCount: _gameState.card.totalSquares,
                          itemBuilder: (context, index) {
                            final row = index ~/ _gameState.card.gridSize;
                            final col = index % _gameState.card.gridSize;
                            final square = _gameState.card.grid[row][col];
                            return _buildEnhancedBingoSquare(square, row, col, index);
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  // Removed _buildCallerInfo - redundant with top progress bar

  // Removed _buildEnhancedGameControls - pause/quit buttons not needed

  // Removed _buildControlButton - no longer needed without pause/quit buttons

  Widget _buildEnhancedBingoSquare(BingoSquare square, int row, int col, int index) {
    final isMarked = square.isMarked;
    final isCenterFree = _gameState.card.type == BingoCardType.small5x5 && row == 2 && col == 2;

    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 300 + (index * 50)), // Staggered animation
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.scale(
          scale: 0.8 + (0.2 * value),
          child: Opacity(
            opacity: value,
            child: GameHoverWidget(
              hoverScale: 1.08,
              onTap: () {
                if (widget.gameMode == GameMode.scratch) {
                  setState(() {
                    _gameState.revealSquare(row, col);
                  });
                  _checkForWin();
                } else {
                  _audioService.playButtonClick();
                }
              },
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: _getSquareColors(isMarked, isCenterFree, square.isRevealed),
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isMarked
                      ? Colors.orange.shade600
                      : Colors.grey.shade300,
                    width: isMarked ? 3 : 1,
                  ),
                  boxShadow: [
                    if (isMarked) BoxShadow(
                      color: Colors.orange.withValues(alpha: 0.4),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // Main content
                    Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (isCenterFree) ...[
                            FreeSpaceWidget(
                              size: _gameState.card.gridSize == 3 ? 40 : 30,
                              isMarked: isMarked,
                            ),
                          ] else if (widget.gameMode == GameMode.scratch && !square.isRevealed) ...[
                            // Scratch overlay - HIDE the food image
                            Container(
                              width: _gameState.card.gridSize == 3 ? 40 : 30,
                              height: _gameState.card.gridSize == 3 ? 40 : 30,
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [Colors.grey.shade400, Colors.grey.shade600],
                                ),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(color: Colors.grey.shade700, width: 2),
                              ),
                              child: const Center(
                                child: Icon(
                                  Icons.touch_app,
                                  color: Colors.white,
                                  size: 20,
                                ),
                              ),
                            ),
                          ] else if (square.isRevealed || widget.gameMode == GameMode.caller) ...[
                            // Food symbol with beautiful emoji/image - ONLY show when revealed or in caller mode
                            Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                FoodImageWidget(
                                  symbol: square.symbol,
                                  size: _gameState.card.gridSize == 3 ? 32 : 24,
                                  isMarked: isMarked,
                                  isRevealed: true,
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  square.symbol.name,
                                  style: TextStyle(
                                    fontSize: _gameState.card.gridSize == 3 ? 9 : 7,
                                    fontWeight: FontWeight.bold,
                                    color: isMarked ? Colors.white : Colors.grey.shade800,
                                  ),
                                  textAlign: TextAlign.center,
                                  maxLines: 2,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ],
                            ),
                          ] else ...[
                            // Empty space for unrevealed scratch squares
                            const SizedBox.shrink(),
                          ],
                        ],
                      ),
                    ),

                    // Check mark for marked squares
                    if (isMarked)
                      Positioned(
                        top: 4,
                        right: 4,
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: BoxDecoration(
                            color: Colors.green.shade500,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.check,
                            size: 12,
                            color: Colors.white,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  List<Color> _getSquareColors(bool isMarked, bool isCenterFree, bool isRevealed) {
    if (isMarked) {
      return [Colors.orange.shade400, Colors.orange.shade600];
    } else if (isCenterFree) {
      return [Colors.orange.shade100, Colors.orange.shade200];
    } else if (widget.gameMode == GameMode.scratch && !isRevealed) {
      return [Colors.grey.shade300, Colors.grey.shade400];
    } else {
      return [Colors.white, Colors.grey.shade50];
    }
  }



  @override
  void dispose() {
    _cardAnimationController.dispose();
    _chefBobAnimationController.dispose();
    _callerService.dispose();
    _audioService.dispose();
    super.dispose();
  }
}