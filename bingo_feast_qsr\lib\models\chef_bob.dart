// 👨‍🍳 Chef Bob - Virtual Caller Character
// This represents the fun chef character who calls out food symbols

import 'food_symbol.dart';

// Chef <PERSON>'s moods/expressions
enum ChefBobMood {
  happy,      // Normal calling
  excited,    // When player is close to winning
  celebrating, // When player wins
  encouraging, // When player hasn't won yet
}

// Chef <PERSON>'s animations
enum ChefBobAnimation {
  idle,           // Standing and waiting
  calling,        // Announcing a symbol
  tossing,        // Tossing food in the air
  pointing,       // Pointing at the bingo card
  celebrating,    // Dancing/cheering
  waving,         // Greeting the player
}

// Chef <PERSON>'s voice lines for different situations
class ChefBobVoiceLines {
  // Greeting messages
  static const List<String> greetings = [
    "Welcome to Bingo Feast! I'm Chef <PERSON>!",
    "Ready to play some delicious bingo?",
    "Let's cook up some fun with bingo!",
    "Time for a tasty game of bingo!",
  ];

  // Calling announcements
  static const List<String> callingPhrases = [
    "Coming right up!",
    "Order up!",
    "Fresh from the kitchen!",
    "Hot and ready!",
    "Served with love!",
    "Chef's special!",
  ];

  // Encouragement when player is close to winning
  static const List<String> encouragement = [
    "You're so close! Keep going!",
    "Almost there! One more symbol!",
    "I can smell victory cooking!",
    "You're on fire! Keep it up!",
    "So close I can taste it!",
  ];

  // Celebration when player wins
  static const List<String> celebrations = [
    "BINGO! You did it! Fantastic!",
    "Winner winner, chicken dinner!",
    "You cooked up a perfect win!",
    "Absolutely delicious victory!",
    "That's what I call a perfect recipe!",
  ];

  // Consolation when player doesn't win
  static const List<String> consolations = [
    "Great game! Here's some points for trying!",
    "You played wonderfully! Come back soon!",
    "Almost had it! Better luck next time!",
    "Thanks for playing! You're always a winner here!",
  ];

  // Symbol-specific announcements
  static Map<String, List<String>> symbolAnnouncements = {
    'burger_classic': [
      "Classic Burger!",
      "Juicy burger coming up!",
      "One delicious burger!",
    ],
    'chicken_crispy': [
      "Crispy Chicken!",
      "Golden crispy chicken!",
      "Finger-lickin' chicken!",
    ],
    'chicken_wings': [
      "Chicken Wings!",
      "Hot wings ready!",
      "Spicy wings on the way!",
    ],
    'fries_regular': [
      "French Fries!",
      "Golden crispy fries!",
      "Hot fries, fresh and salty!",
    ],
    'soda_cola': [
      "Cola!",
      "Ice-cold cola!",
      "Refreshing soda!",
    ],
    'ice_cream': [
      "Ice Cream!",
      "Sweet and creamy!",
      "Cool ice cream treat!",
    ],
    // Add more as needed...
  };

  // Get random greeting
  static String getRandomGreeting() {
    final mutableGreetings = List<String>.from(greetings);
    mutableGreetings.shuffle();
    return mutableGreetings.first;
  }

  // Get random calling phrase
  static String getRandomCallingPhrase() {
    final mutablePhrases = List<String>.from(callingPhrases);
    mutablePhrases.shuffle();
    return mutablePhrases.first;
  }

  // Get symbol-specific announcement
  static String getSymbolAnnouncement(FoodSymbol symbol) {
    final announcements = symbolAnnouncements[symbol.id];
    if (announcements != null && announcements.isNotEmpty) {
      final mutableAnnouncements = List<String>.from(announcements);
      mutableAnnouncements.shuffle();
      return mutableAnnouncements.first;
    }
    return symbol.name; // Fallback to symbol name
  }

  // Get encouragement message
  static String getRandomEncouragement() {
    final mutableEncouragement = List<String>.from(encouragement);
    mutableEncouragement.shuffle();
    return mutableEncouragement.first;
  }

  // Get celebration message
  static String getRandomCelebration() {
    final mutableCelebrations = List<String>.from(celebrations);
    mutableCelebrations.shuffle();
    return mutableCelebrations.first;
  }

  // Get consolation message
  static String getRandomConsolation() {
    final mutableConsolations = List<String>.from(consolations);
    mutableConsolations.shuffle();
    return mutableConsolations.first;
  }
}

// Main Chef Bob character class
class ChefBob {
  ChefBobMood currentMood;
  ChefBobAnimation currentAnimation;
  String currentMessage;
  bool isVisible;
  bool isSpeaking;

  ChefBob({
    this.currentMood = ChefBobMood.happy,
    this.currentAnimation = ChefBobAnimation.idle,
    this.currentMessage = "",
    this.isVisible = true,
    this.isSpeaking = false,
  });

  // Make Chef Bob greet the player
  void greet() {
    currentMood = ChefBobMood.happy;
    currentAnimation = ChefBobAnimation.waving;
    currentMessage = ChefBobVoiceLines.getRandomGreeting();
    isSpeaking = true;
  }

  // Make Chef Bob call a symbol
  void callSymbol(FoodSymbol symbol) {
    currentMood = ChefBobMood.happy;
    currentAnimation = ChefBobAnimation.calling;

    // Create the full announcement
    final callingPhrase = ChefBobVoiceLines.getRandomCallingPhrase();
    final symbolAnnouncement = ChefBobVoiceLines.getSymbolAnnouncement(symbol);
    currentMessage = "$callingPhrase $symbolAnnouncement";

    isSpeaking = true;
  }

  // Make Chef Bob encourage the player
  void encourage() {
    currentMood = ChefBobMood.excited;
    currentAnimation = ChefBobAnimation.pointing;
    currentMessage = ChefBobVoiceLines.getRandomEncouragement();
    isSpeaking = true;
  }

  // Make Chef Bob celebrate a win
  void celebrate() {
    currentMood = ChefBobMood.celebrating;
    currentAnimation = ChefBobAnimation.celebrating;
    currentMessage = ChefBobVoiceLines.getRandomCelebration();
    isSpeaking = true;
  }

  // Make Chef Bob console the player
  void console() {
    currentMood = ChefBobMood.encouraging;
    currentAnimation = ChefBobAnimation.waving;
    currentMessage = ChefBobVoiceLines.getRandomConsolation();
    isSpeaking = true;
  }

  // Make Chef Bob do a fun animation
  void doFunAnimation() {
    currentAnimation = ChefBobAnimation.tossing;
    currentMessage = "Watch me toss this delicious food!";
    isSpeaking = true;
  }

  // Stop Chef Bob from speaking
  void stopSpeaking() {
    isSpeaking = false;
    currentAnimation = ChefBobAnimation.idle;
  }

  // Hide Chef Bob
  void hide() {
    isVisible = false;
    stopSpeaking();
  }

  // Show Chef Bob
  void show() {
    isVisible = true;
    currentAnimation = ChefBobAnimation.idle;
  }

  // Reset Chef Bob to default state
  void reset() {
    currentMood = ChefBobMood.happy;
    currentAnimation = ChefBobAnimation.idle;
    currentMessage = "";
    isVisible = true;
    isSpeaking = false;
  }

  // Get Chef Bob's current state for UI
  Map<String, dynamic> getCurrentState() {
    return {
      'mood': currentMood.name,
      'animation': currentAnimation.name,
      'message': currentMessage,
      'isVisible': isVisible,
      'isSpeaking': isSpeaking,
    };
  }

  // Convert to JSON for saving state
  Map<String, dynamic> toJson() {
    return {
      'currentMood': currentMood.name,
      'currentAnimation': currentAnimation.name,
      'currentMessage': currentMessage,
      'isVisible': isVisible,
      'isSpeaking': isSpeaking,
    };
  }

  factory ChefBob.fromJson(Map<String, dynamic> json) {
    return ChefBob(
      currentMood: ChefBobMood.values.firstWhere(
        (e) => e.name == json['currentMood'],
        orElse: () => ChefBobMood.happy,
      ),
      currentAnimation: ChefBobAnimation.values.firstWhere(
        (e) => e.name == json['currentAnimation'],
        orElse: () => ChefBobAnimation.idle,
      ),
      currentMessage: json['currentMessage'] ?? '',
      isVisible: json['isVisible'] ?? true,
      isSpeaking: json['isSpeaking'] ?? false,
    );
  }

  @override
  String toString() {
    return 'ChefBob(mood: $currentMood, animation: $currentAnimation, speaking: $isSpeaking)';
  }
}