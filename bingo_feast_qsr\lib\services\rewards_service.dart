// 🎁 Rewards Service
// This handles the reward system with proper win rates and prize distribution

import 'dart:math';
import '../models/bingo_card.dart';
import '../models/game_state.dart';

// Win rate configuration class
class WinRateConfig {
  final double totalWinRate;
  final double smallWinRate;
  final double mediumWinRate;
  final double bigWinRate;

  const WinRateConfig({
    required this.totalWinRate,
    required this.smallWinRate,
    required this.mediumWinRate,
    required this.bigWinRate,
  });
}

class RewardsService {
  static final RewardsService _instance = RewardsService._internal();
  factory RewardsService() => _instance;
  RewardsService._internal();

  // Win rate configuration
  static const Map<BingoCardType, WinRateConfig> _winRateConfigs = {
    BingoCardType.small5x5: WinRateConfig(
      totalWinRate: 0.65, // 65% win rate
      smallWinRate: 0.50,  // 50% of games = small wins
      mediumWinRate: 0.15, // 15% of games = medium wins
      bigWinRate: 0.05,    // 5% of games = big wins
      // Remaining 35% = no win (consolation points)
    ),
    BingoCardType.large8x8: WinRateConfig(
      totalWinRate: 0.40, // 40% win rate
      smallWinRate: 0.30,  // 30% of games = small wins
      mediumWinRate: 0.08, // 8% of games = medium wins
      bigWinRate: 0.02,    // 2% of games = big wins
      // Remaining 60% = no win (consolation points)
    ),
  };

  // Calculate if player should win and what type of reward
  GameReward calculateReward(BingoCard card, GameMode gameMode) {
    final config = _winRateConfigs[card.type]!;
    final random = Random();
    final roll = random.nextDouble();

    // Determine win type based on configured rates
    if (roll < config.bigWinRate) {
      return _generateBigReward(card, gameMode);
    } else if (roll < config.bigWinRate + config.mediumWinRate) {
      return _generateMediumReward(card, gameMode);
    } else if (roll < config.totalWinRate) {
      return _generateSmallReward(card, gameMode);
    } else {
      return _generateConsolationReward(card, gameMode);
    }
  }

  // Generate small rewards (10-50 points, free side items, 10% off)
  GameReward _generateSmallReward(BingoCard card, GameMode gameMode) {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Points reward (10-50 points)
        final points = 10 + random.nextInt(41);
        return GameReward(
          type: RewardType.points,
          description: 'You earned $points loyalty points!',
          value: points,
        );
      case 1:
        // Free side item
        final sideItems = ['French Fries', 'Onion Rings', 'Coleslaw', 'Mac & Cheese'];
        final item = sideItems[random.nextInt(sideItems.length)];
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free $item!',
          value: 0,
          itemId: item.toLowerCase().replaceAll(' ', '_'),
          code: 'BINGO${_generateCode(4)}',
        );
      default:
        // 10% discount
        return GameReward(
          type: RewardType.discount,
          description: '10% off your next order!',
          value: 10,
          code: 'SAVE10${_generateCode(3)}',
        );
    }
  }

  // Generate medium rewards (free combo items, 20% off, bonus cards)
  GameReward _generateMediumReward(BingoCard card, GameMode gameMode) {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Free main item
        final mainItems = ['Classic Burger', 'Crispy Chicken', 'Fish Sandwich'];
        final item = mainItems[random.nextInt(mainItems.length)];
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free $item!',
          value: 0,
          itemId: item.toLowerCase().replaceAll(' ', '_'),
          code: 'MAIN${_generateCode(4)}',
        );
      case 1:
        // 20% discount
        return GameReward(
          type: RewardType.discount,
          description: '20% off your next order!',
          value: 20,
          code: 'SAVE20${_generateCode(3)}',
        );
      default:
        // Bonus card
        return GameReward(
          type: RewardType.bonusCard,
          description: 'Free bonus bingo card!',
          value: 1,
          code: 'BONUS${_generateCode(3)}',
        );
    }
  }

  // Generate big rewards (free combo meals, $10 gift cards, exclusive items)
  GameReward _generateBigReward(BingoCard card, GameMode gameMode) {
    final random = Random();
    final rewardType = random.nextInt(3);

    switch (rewardType) {
      case 0:
        // Free combo meal
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free Combo Meal!',
          value: 0,
          itemId: 'combo_meal',
          code: 'COMBO${_generateCode(4)}',
        );
      case 1:
        // $10 gift card
        return GameReward(
          type: RewardType.giftCard,
          description: '\$10 Gift Card!',
          value: 10,
          code: 'GIFT${_generateCode(4)}',
        );
      default:
        // Exclusive item (special for wing theme)
        return GameReward(
          type: RewardType.freeItem,
          description: 'Free Exclusive Wings Platter!',
          value: 0,
          itemId: 'wings_exclusive',
          code: 'WINGS${_generateCode(4)}',
        );
    }
  }

  // Generate consolation reward (always points)
  GameReward _generateConsolationReward(BingoCard card, GameMode gameMode) {
    final points = card.type == BingoCardType.small5x5 ? 5 : 10;
    return GameReward(
      type: RewardType.points,
      description: 'Thanks for playing! Here are some points.',
      value: points,
    );
  }

  // Generate random code
  String _generateCode(int length) {
    final random = Random();
    return random.nextInt(pow(10, length).toInt()).toString().padLeft(length, '0');
  }

  // Test win rates (for validation)
  Map<String, double> testWinRates(BingoCardType cardType, int iterations) {
    int bigWins = 0;
    int mediumWins = 0;
    int smallWins = 0;
    int noWins = 0;

    for (int i = 0; i < iterations; i++) {
      final testCard = BingoCard.generate(
        userId: 'test',
        type: cardType,
        tier: BingoCardTier.bronze,
      );

      final reward = calculateReward(testCard, GameMode.caller);

      switch (reward.type) {
        case RewardType.giftCard:
        case RewardType.freeItem when reward.description.contains('Combo') ||
                                     reward.description.contains('Wings'):
          bigWins++;
          break;
        case RewardType.freeItem when reward.description.contains('Burger') ||
                                     reward.description.contains('Chicken'):
        case RewardType.discount when reward.value == 20:
        case RewardType.bonusCard:
          mediumWins++;
          break;
        case RewardType.freeItem:
        case RewardType.discount when reward.value == 10:
        case RewardType.points when reward.value > 10:
          smallWins++;
          break;
        default:
          noWins++;
          break;
      }
    }

    return {
      'bigWinRate': bigWins / iterations,
      'mediumWinRate': mediumWins / iterations,
      'smallWinRate': smallWins / iterations,
      'noWinRate': noWins / iterations,
      'totalWinRate': (bigWins + mediumWins + smallWins) / iterations,
    };
  }
}